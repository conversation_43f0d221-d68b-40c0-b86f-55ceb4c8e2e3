// src/features/task-management/components/components/task-list-stacked-content.tsx
'use client';

import React, { useMemo } from 'react';
import {
  ColumnDef,
  OnChangeFn,
  ColumnSizingState,
  VisibilityState,
  ColumnOrderState,
} from '@tanstack/react-table';
import { useReactTable, getCoreRowModel, flexRender } from '@tanstack/react-table';

import { cn } from '@/core/lib/utils';
import type {
  DensityStyleValues,
  Task,
  TaskGroup,
  TaskGroupConfig,
  Vehicle,
  CustomColumnDefinition,
  VehicleDisplayMode,
  InTaskVehicleCardStyle,
  TaskListDensityMode,
} from '@/core/types';
import { useTaskHighlight } from '@/shared/components/contexts/TaskRowHighlightContext';
import {
  useTaskSelectionState,
  useTaskSelectionActions,
} from '@/shared/components/contexts/TaskSelectionContext';

import { DispatchedVehiclesCell } from '../cells/DispatchedVehiclesCell';
import { ProductionLinesCell } from '../cells/ProductionLinesCell';

import { TaskGroupHeader } from '../task-group-header';

interface TaskListStackedContentProps {
  tasks: Task[];
  taskGroups: TaskGroup[];
  tableColumns: ColumnDef<Task>[];
  enableZebraStriping: boolean;
  densityStyles: DensityStyleValues;
  groupConfig?: TaskGroupConfig;
  tableTotalWidth: number;
  estimateRowHeight: (task?: Task) => number;
  columnSizing: Record<string, number>;
  columnVisibility: Record<string, boolean>;
  columnOrder: string[];
  onColumnSizingChange: OnChangeFn<ColumnSizingState>;
  onColumnOrderChange: OnChangeFn<ColumnOrderState>;
  onColumnVisibilityChange: OnChangeFn<VisibilityState>;
  onHeaderContextMenu?: (event: React.MouseEvent, columnDef: CustomColumnDefinition) => void;
  onHeaderDoubleClick?: (event: React.MouseEvent, columnDef: CustomColumnDefinition) => void;
  onRowContextMenu?: (event: React.MouseEvent, row: any) => void;
  onRowDoubleClick?: (row: any) => void;
  onDropOnProductionLine?: (vehicle: any, taskId: string, lineId: string) => void;
  onToggleGroupCollapse?: (groupKey: string) => void;
  onCancelGrouping?: () => void;
  // Props for DispatchedVehiclesCell
  vehicleDisplayMode: VehicleDisplayMode;
  inTaskVehicleCardStyles?: InTaskVehicleCardStyle;
  productionLineCount: number;
  density: Exclude<TaskListDensityMode, '' | 'card'>;
  onCancelVehicleDispatch: (vehicleId: string) => void;
  onOpenStyleEditor: () => void;
  onOpenDeliveryOrderDetails: (vehicle: Vehicle, task: Task) => void;
  onOpenVehicleCardContextMenu: (event: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
  getColumnBackgroundProps?: (
    columnId: string,
    isHeader: boolean,
    isFixed: boolean
  ) => {
    style: React.CSSProperties;
    className: string;
  };
  getCellTextClasses?: (columnId: string) => string;
  className?: string;
}

export function TaskListStackedContent({
  tasks,
  taskGroups,
  tableColumns,
  enableZebraStriping,
  densityStyles,
  groupConfig,
  tableTotalWidth,
  estimateRowHeight,
  columnSizing,
  columnVisibility,
  columnOrder,
  onColumnSizingChange,
  onColumnOrderChange,
  onColumnVisibilityChange,
  onHeaderContextMenu,
  onHeaderDoubleClick,
  onRowContextMenu,
  onRowDoubleClick,
  onDropOnProductionLine,
  onToggleGroupCollapse,
  onCancelGrouping,
  getColumnBackgroundProps,
  getCellTextClasses,
  className,
  // New props for cell components
  vehicleDisplayMode,
  inTaskVehicleCardStyles,
  productionLineCount,
  density,
  onCancelVehicleDispatch,
  onOpenStyleEditor,
  onOpenDeliveryOrderDetails,
  onOpenVehicleCardContextMenu,
}: TaskListStackedContentProps) {
  // 提供默认的车辆卡片样式设置
  const defaultInTaskVehicleCardStyles: InTaskVehicleCardStyle = {
    cardWidth: 'w-14',
    cardHeight: 'h-8',
    fontSize: 'text-[12px]',
    fontColor: 'text-foreground',
    vehicleNumberFontWeight: 'font-medium',
    cardBgColor: 'bg-card/80',
    statusDotSize: 'w-1 h-1',
    borderRadius: 'rounded-md',
    boxShadow: 'shadow-sm',
    vehiclesPerRow: 4,
    gap: 0,
    cardSize: 'small',
  };

  const effectiveInTaskVehicleCardStyles =
    inTaskVehicleCardStyles || defaultInTaskVehicleCardStyles;

  // 分离主表格列和固定列（调度车辆、生产线）
  const { mainColumns, stackedColumns } = useMemo(() => {
    const main: ColumnDef<Task>[] = [];
    const stacked: ColumnDef<Task>[] = [];

    tableColumns.forEach(column => {
      const customDef = (column.meta as any)?.customDef;
      if (customDef?.id === 'dispatchedVehicles' || customDef?.id === 'productionLines') {
        stacked.push(column);
      } else {
        main.push(column);
      }
    });

    return { mainColumns: main, stackedColumns: stacked };
  }, [tableColumns]);

  // 计算主表格宽度（排除固定列）
  const mainTableWidth = useMemo(() => {
    const stackedColumnIds = stackedColumns.map(col => (col.meta as any)?.customDef?.id);
    let width = tableTotalWidth;

    stackedColumnIds.forEach(columnId => {
      if (columnSizing[columnId]) {
        width -= columnSizing[columnId];
      }
    });

    return Math.max(width, 600); // 最小宽度
  }, [tableTotalWidth, stackedColumns, columnSizing]);

  if (groupConfig?.enabled) {
    return (
      <div className={cn('flex flex-col h-full min-h-0', className)}>
        <div className='flex-1 overflow-auto custom-scrollbar min-h-0'>
          <div className='space-y-2 p-2'>
            {taskGroups.map(group => (
              <div key={`group-${group.key}-${group.tasks.length}`} className='border rounded-lg'>
                <TaskGroupHeader
                  group={group}
                  groupConfig={groupConfig}
                  onToggleCollapse={onToggleGroupCollapse}
                  onCancelGrouping={onCancelGrouping}
                />
                {!group.collapsed && group.tasks.length > 0 && (
                  <div className='border-t' key={`stacked-table-${group.key}`}>
                    <StackedRowsTable
                      data={group.tasks}
                      mainColumns={mainColumns}
                      stackedColumns={stackedColumns}
                      getRowId={(row: Task) => row.id}
                      densityStyles={densityStyles}
                      enableZebraStriping={enableZebraStriping}
                      estimateRowHeightAction={estimateRowHeight}
                      tableTotalWidth={tableTotalWidth}
                      columnSizing={columnSizing}
                      onColumnSizingChangeAction={onColumnSizingChange}
                      columnVisibility={columnVisibility}
                      onColumnVisibilityChangeAction={onColumnVisibilityChange}
                      columnOrder={columnOrder}
                      onColumnOrderChangeAction={onColumnOrderChange}
                      onHeaderContextMenuAction={onHeaderContextMenu}
                      onHeaderDoubleClickAction={onHeaderDoubleClick}
                      onRowContextMenuAction={onRowContextMenu}
                      onRowDoubleClickAction={onRowDoubleClick}
                      onDropOnProductionLineAction={onDropOnProductionLine}
                      getColumnBackgroundPropsAction={getColumnBackgroundProps}
                      getCellTextClassesAction={getCellTextClasses}
                      vehicleDisplayMode={vehicleDisplayMode}
                      inTaskVehicleCardStyles={effectiveInTaskVehicleCardStyles}
                      productionLineCount={productionLineCount}
                      density={density}
                      onCancelVehicleDispatch={onCancelVehicleDispatch}
                      onOpenStyleEditor={onOpenStyleEditor}
                      onOpenDeliveryOrderDetails={onOpenDeliveryOrderDetails}
                      onOpenVehicleCardContextMenu={onOpenVehicleCardContextMenu}
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('flex flex-col h-full min-h-0', className)}>
      <div className='flex-1 custom-scrollbar overflow-auto min-h-0'>
        <StackedRowsTable
          data={tasks}
          mainColumns={mainColumns}
          stackedColumns={stackedColumns}
          getRowId={row => row.id}
          densityStyles={densityStyles}
          enableZebraStriping={enableZebraStriping}
          estimateRowHeightAction={estimateRowHeight}
          tableTotalWidth={tableTotalWidth}
          columnSizing={columnSizing}
          onColumnSizingChangeAction={onColumnSizingChange}
          columnVisibility={columnVisibility}
          onColumnVisibilityChangeAction={onColumnVisibilityChange}
          columnOrder={columnOrder}
          onColumnOrderChangeAction={onColumnOrderChange}
          onHeaderContextMenuAction={onHeaderContextMenu}
          onHeaderDoubleClickAction={onHeaderDoubleClick}
          onRowContextMenuAction={onRowContextMenu}
          onRowDoubleClickAction={onRowDoubleClick}
          onDropOnProductionLineAction={onDropOnProductionLine}
          getColumnBackgroundPropsAction={getColumnBackgroundProps}
          getCellTextClassesAction={getCellTextClasses}
          vehicleDisplayMode={vehicleDisplayMode}
          inTaskVehicleCardStyles={effectiveInTaskVehicleCardStyles}
          productionLineCount={productionLineCount}
          density={density}
          onCancelVehicleDispatch={onCancelVehicleDispatch}
          onOpenStyleEditor={onOpenStyleEditor}
          onOpenDeliveryOrderDetails={onOpenDeliveryOrderDetails}
          onOpenVehicleCardContextMenu={onOpenVehicleCardContextMenu}
        />
      </div>
    </div>
  );
}

// 堆叠行表格组件 - 每条数据用两行显示
interface StackedRowsTableProps<TData extends object> {
  data: TData[];
  mainColumns: ColumnDef<TData>[];
  stackedColumns: ColumnDef<TData>[];
  getRowId: (row: TData) => string;
  densityStyles: DensityStyleValues;
  enableZebraStriping: boolean;
  estimateRowHeightAction: (task?: TData) => number;
  tableTotalWidth: number;
  columnSizing: Record<string, number>;
  onColumnSizingChangeAction: OnChangeFn<ColumnSizingState>;
  columnVisibility: VisibilityState;
  onColumnVisibilityChangeAction: OnChangeFn<VisibilityState>;
  columnOrder: ColumnOrderState;
  onColumnOrderChangeAction: OnChangeFn<ColumnOrderState>;
  onHeaderContextMenuAction?: (event: React.MouseEvent, columnDef: CustomColumnDefinition) => void;
  onHeaderDoubleClickAction?: (event: React.MouseEvent, columnDef: CustomColumnDefinition) => void;
  onRowContextMenuAction?: (event: React.MouseEvent, row: any) => void;
  onRowDoubleClickAction?: (row: any) => void;
  onDropOnProductionLineAction?: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  getColumnBackgroundPropsAction?: (
    columnId: string,
    isHeader: boolean,
    isFixed: boolean
  ) => {
    style: React.CSSProperties;
    className: string;
  };
  getCellTextClassesAction?: (columnId: string) => string;
  // Props for cell components
  vehicleDisplayMode: VehicleDisplayMode;
  inTaskVehicleCardStyles?: InTaskVehicleCardStyle;
  productionLineCount: number;
  density: Exclude<TaskListDensityMode, '' | 'card'>;
  onCancelVehicleDispatch: (vehicleId: string) => void;
  onOpenStyleEditor: () => void;
  onOpenDeliveryOrderDetails: (vehicle: Vehicle, task: Task) => void;
  onOpenVehicleCardContextMenu: (event: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
}

function StackedRowsTable<TData extends object>({
  data,
  mainColumns,
  stackedColumns,
  getRowId,
  densityStyles,
  enableZebraStriping,
  estimateRowHeightAction,
  tableTotalWidth,
  columnSizing,
  onColumnSizingChangeAction,
  columnVisibility,
  onColumnVisibilityChangeAction,
  columnOrder,
  onColumnOrderChangeAction,
  onHeaderContextMenuAction,
  onHeaderDoubleClickAction,
  onRowContextMenuAction,
  onRowDoubleClickAction,
  onDropOnProductionLineAction,
  getColumnBackgroundPropsAction,
  getCellTextClassesAction,
  vehicleDisplayMode,
  inTaskVehicleCardStyles,
  productionLineCount,
  density,
  onCancelVehicleDispatch,
  onOpenStyleEditor,
  onOpenDeliveryOrderDetails,
  onOpenVehicleCardContextMenu,
}: StackedRowsTableProps<TData>) {
  // 提供默认的车辆卡片样式设置
  const defaultInTaskVehicleCardStyles: InTaskVehicleCardStyle = {
    cardWidth: 'w-14',
    cardHeight: 'h-8',
    fontSize: 'text-[12px]',
    fontColor: 'text-foreground',
    vehicleNumberFontWeight: 'font-medium',
    cardBgColor: 'bg-card/80',
    statusDotSize: 'w-1 h-1',
    borderRadius: 'rounded-md',
    boxShadow: 'shadow-sm',
    vehiclesPerRow: 4,
    gap: 0,
    cardSize: 'small',
  };

  const effectiveInTaskVehicleCardStyles =
    inTaskVehicleCardStyles || defaultInTaskVehicleCardStyles;

  // 使用任务高亮功能
  const { setHighlightedTaskId } = useTaskHighlight();

  // 使用任务选中功能
  const { selectedTaskId } = useTaskSelectionState();
  const { setSelectedTask } = useTaskSelectionActions();

  // 创建表格实例用于获取表头
  const table = useReactTable({
    data,
    columns: mainColumns,
    getCoreRowModel: getCoreRowModel(),
    getRowId,
    state: {
      columnSizing,
      columnVisibility,
      columnOrder,
    },
    onColumnSizingChange: onColumnSizingChangeAction,
    onColumnVisibilityChange: onColumnVisibilityChangeAction,
    onColumnOrderChange: onColumnOrderChangeAction,
    columnResizeMode: 'onChange',
    enableColumnResizing: true,
  });

  return (
    <div className='stacked-table-container h-full overflow-y-auto overflow-x-hidden'>
      {/* 使用标准表格元素，样式与普通表格保持一致 */}
      {/* 设置表格宽度为100%以适应容器宽度，禁止横向滚动 */}
      <table
        className='virtualized-table w-full border-collapse'
        style={{ width: '100%', tableLayout: 'fixed' }}
        onContextMenu={e => {
          e.preventDefault();
          e.stopPropagation();
        }}
      >
        {/* 表头 */}
        <thead className='sticky top-0 z-10 bg-background'>
          {/* 主列表头 */}
          <tr className='border-b'>
            {table.getHeaderGroups()[0]?.headers.map(header => {
              const customDef = (header.column.columnDef.meta as any)?.customDef;

              // 获取表头文本
              const headerText =
                customDef?.header ||
                (typeof header.column.columnDef.header === 'string'
                  ? header.column.columnDef.header
                  : typeof header.column.columnDef.header === 'function'
                    ? flexRender(header.column.columnDef.header, header.getContext())
                    : header.column.id);

              const backgroundProps = getColumnBackgroundPropsAction?.(
                customDef?.id || header.column.id,
                true,
                false
              ) || {
                style: {},
                className: '',
              };
              let width = (header.column.getSize() / tableTotalWidth) * 100;
              if (width < 1.5) {
                width = 1.5;
              }
              return (
                <th
                  key={header.id}
                  className={cn(
                    'relative border-r text-left font-semibold text-foreground/90',
                    'hover:bg-muted/50 transition-colors bg-muted/50',
                    backgroundProps.className
                  )}
                  style={{
                    width: `${width}%`,
                    padding: '2px 4px',
                    fontSize: '11px',
                    height: '28px',
                    minHeight: '0',
                    maxHeight: '28px',
                    lineHeight: '1.1',
                    ...backgroundProps.style,
                  }}
                  onContextMenu={e => customDef && onHeaderContextMenuAction?.(e, customDef)}
                  onDoubleClick={e => customDef && onHeaderDoubleClickAction?.(e, customDef)}
                >
                  <span className='truncate'>{headerText}</span>
                  {header.column.getCanResize() && (
                    <div
                      className='absolute right-0 top-0 h-full w-1 cursor-col-resize bg-border/50 opacity-0 hover:opacity-100 hover:bg-primary/50 transition-all'
                      onMouseDown={header.getResizeHandler()}
                    />
                  )}
                </th>
              );
            })}
          </tr>
        </thead>

        {/* 数据行 - 每条数据用两行显示 */}
        <tbody>
          {data.map((row, index) => {
            const task = row as unknown as Task;
            const vehicles = (task as any).vehicles || [];
            const isEven = index % 2 === 0;
            const bgClass = enableZebraStriping && isEven ? 'bg-muted/30' : '';
            const isSelected = selectedTaskId === task.id;

            return (
              <React.Fragment key={getRowId(row)}>
                {/* 第一行：主要数据 */}
                <tr
                  className={cn(
                    'hover:bg-muted/50 transition-colors h-8 cursor-pointer',
                    bgClass,
                    isSelected && 'bg-primary/10 stacked-row-selected-first'
                  )}
                  data-task-id={task.id}
                  data-row-id={task.id}
                  onClick={e => {
                    // 避免在点击交互元素时触发选中
                    const target = e.target as HTMLElement;
                    if (target.closest('button, a, input, select, textarea, [role="button"]')) {
                      return;
                    }
                    setSelectedTask(isSelected ? null : task);
                  }}
                  onContextMenu={e => {
                    // 检查事件是否来自车辆卡片，如果是则不处理
                    const target = e.target as HTMLElement;
                    const isVehicleCard = target.closest('[data-vehicle-card="true"]');

                    if (isVehicleCard) {
                      console.log('🚫 堆叠模式第一行: 事件来自车辆卡片，跳过处理');
                      return; // 不处理来自车辆卡片的事件
                    }

                    console.log('📋 堆叠模式第一行右键菜单被触发');
                    e.preventDefault();
                    e.stopPropagation();
                    onRowContextMenuAction?.(e, { original: task } as any);
                  }}
                  onDoubleClick={() => onRowDoubleClickAction?.({ original: task } as any)}
                >
                  {table
                    .getRowModel()
                    .rows[index]?.getVisibleCells()
                    .map(cell => {
                      const customDef = (cell.column.columnDef.meta as any)?.customDef;
                      if (!customDef) return null;
                      const cellWidth: number = cell.column.getSize();

                      let cellWidthPX = (cellWidth / tableTotalWidth) * 100;

                      if (cellWidthPX < 1.5) {
                        cellWidthPX = 1.5;
                      }
                      const backgroundProps = getColumnBackgroundPropsAction?.(
                        customDef.id,
                        false,
                        false
                      ) || {
                        style: {},
                        className: '',
                      };

                      return (
                        <td
                          key={cell.id}
                          className={cn(
                            'border-r',
                            backgroundProps.className,
                            getCellTextClassesAction?.(customDef.id) || '',
                            isSelected && 'stacked-cell-selected-first',
                            // 消息列需要特殊处理，确保角标不被裁剪
                            customDef.id === 'messages' && 'overflow-visible relative'
                          )}
                          style={{
                            width: `${cellWidthPX}%`,
                            padding: `${densityStyles.cellPaddingY} ${densityStyles.cellPaddingX}`,
                            ...backgroundProps.style,
                          }}
                        >
                          {(() => {
                            // 需要居中对齐的列：消息、提醒、车数
                            const centerAlignColumns = [
                              'messages',
                              'dispatchReminder',
                              'vehicleCount',
                            ];
                            const shouldCenter = centerAlignColumns.includes(customDef.id);

                            // 消息列需要特殊处理，确保角标显示完整
                            if (customDef.id === 'messages') {
                              return (
                                <div className='flex items-center justify-center h-full w-full relative overflow-visible'>
                                  <div className='relative'>
                                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                  </div>
                                </div>
                              );
                            }
                            // 进度列需要特殊处理，优化进度条样式
                            else if (customDef.id === 'completedProgress') {
                              return (
                                <div className='flex items-center justify-center h-full w-full px-2'>
                                  <div className='w-full max-w-[60px]'>
                                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                  </div>
                                </div>
                              );
                            } else if (shouldCenter) {
                              return (
                                <div className='flex items-center justify-center h-full w-full'>
                                  <div className='truncate text-center text-ellipsis whitespace-nowrap'>
                                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                  </div>
                                </div>
                              );
                            } else {
                              return (
                                <div className='flex items-center justify-start h-full w-full'>
                                  <div className='truncate text-left text-ellipsis whitespace-nowrap'>
                                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                  </div>
                                </div>
                              );
                            }
                          })()}
                        </td>
                      );
                    })}
                </tr>

                {/* 第二行：堆叠列（调度车辆和生产线） */}
                <tr
                  className={cn(
                    'hover:bg-muted/50 transition-colors h-8 border-t-0 cursor-pointer',
                    bgClass,
                    isSelected && 'bg-primary/10 stacked-row-selected-second'
                  )}
                  data-task-id={task.id}
                  data-row-id={task.id}
                  onClick={e => {
                    // 避免在点击交互元素时触发选中
                    const target = e.target as HTMLElement;
                    if (target.closest('button, a, input, select, textarea, [role="button"]')) {
                      return;
                    }
                    setSelectedTask(isSelected ? null : task);
                  }}
                  onContextMenu={e => {
                    // 检查事件是否来自车辆卡片，如果是则不处理
                    const target = e.target as HTMLElement;
                    const isVehicleCard = target.closest('[data-vehicle-card="true"]');

                    if (isVehicleCard) {
                      console.log('🚫 堆叠模式第二行: 事件来自车辆卡片，跳过处理');
                      return; // 不处理来自车辆卡片的事件
                    }

                    console.log('📋 堆叠模式第二行右键菜单被触发');
                    e.preventDefault();
                    e.stopPropagation();
                    onRowContextMenuAction?.(e, { original: task } as any);
                  }}
                  onDoubleClick={() => onRowDoubleClickAction?.({ original: task } as any)}
                >
                  {/* 调度车辆列 - 跨越除最后一列外的所有列 */}
                  <td
                    colSpan={Math.max(1, (table.getHeaderGroups()[0]?.headers.length || 0) - 1)}
                    className={cn('border-r pl-8', isSelected && 'stacked-cell-selected-second')}
                    style={{
                      minHeight: '32px', // 设置最小高度确保一致性
                    }}
                    onContextMenu={e => {
                      // 检查事件是否来自车辆卡片，如果是则不处理
                      const target = e.target as HTMLElement;
                      const isVehicleCard = target.closest('[data-vehicle-card="true"]');

                      if (isVehicleCard) {
                        console.log('🚫 堆叠模式车辆单元格: 事件来自车辆卡片，跳过处理');
                        return; // 不处理来自车辆卡片的事件
                      }

                      console.log('📋 堆叠模式车辆单元格右键菜单被触发');
                      e.preventDefault();
                      e.stopPropagation();
                      onRowContextMenuAction?.(e, { original: task } as any);
                    }}
                  >
                    <DispatchedVehiclesCell
                      task={task}
                      taskVehicles={vehicles}
                      vehicleDisplayMode={vehicleDisplayMode}
                      inTaskVehicleCardStyles={effectiveInTaskVehicleCardStyles}
                      productionLineCount={productionLineCount}
                      density={density}
                      onCancelDispatch={onCancelVehicleDispatch}
                      onOpenStyleEditor={onOpenStyleEditor}
                      onOpenDeliveryOrderDetails={onOpenDeliveryOrderDetails}
                      onOpenContextMenu={onOpenVehicleCardContextMenu}
                    />
                  </td>

                  {/* 生产线列 - 跨越最后一列 */}
                  <td
                    colSpan={1}
                    className={cn('border-r', isSelected && 'stacked-cell-selected-second')}
                    style={{
                      padding: `${densityStyles.cellPaddingY} ${densityStyles.cellPaddingX}`,
                      minHeight: '32px', // 设置最小高度确保一致性
                    }}
                    onContextMenu={e => {
                      e.preventDefault();
                      e.stopPropagation();
                      onRowContextMenuAction?.(e, { original: task } as any);
                    }}
                  >
                    <ProductionLinesCell
                      task={task}
                      productionLineCount={productionLineCount}
                      densityStyles={densityStyles}
                      onDropVehicleOnLine={onDropOnProductionLineAction || (() => { })}
                    />
                  </td>
                </tr>
              </React.Fragment>
            );
          })}
        </tbody>
      </table>
    </div>
  );
}
