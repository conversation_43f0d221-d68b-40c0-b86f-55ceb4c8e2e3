# Stacked模式车辆卡片样式修复

## 问题描述

在stacked模式下，车辆卡片的样式没有跟着车卡样式设置走。用户配置的车辆卡片样式设置（如背景色、大小、字体等）在stacked模式下没有生效。

## 问题原因

问题有两个层面：

### 1. 数据传递链断裂

在 `TaskListContainer.tsx` 中，传递给 `TaskListBody` 的 `settings` 对象缺少了 `inTaskVehicleCardStyles`：

```typescript
// 问题代码 - 缺少 inTaskVehicleCardStyles
settings={{
  columnWidths: taskListSettings.settings.columnWidths,
  columnOrder: taskListSettings.settings.columnOrder,
  columnVisibility: taskListSettings.settings.columnVisibility,
  enableZebraStriping: taskListSettings.settings.enableZebraStriping,
  groupConfig: taskListSettings.settings.groupConfig,
  // 缺少: inTaskVehicleCardStyles
}}
```

### 2. 硬编码的fallback对象

在 `task-list-content-renderer.tsx` 中，stacked模式使用了一个硬编码的fallback对象：

```typescript
// 问题代码
inTaskVehicleCardStyles={
  settings.inTaskVehicleCardStyles || {
    cardWidth: 'w-14',
    cardHeight: 'h-8',
    fontSize: 'text-[12px]',
    fontColor: 'text-foreground',
    vehicleNumberFontWeight: 'font-medium',
    cardBgColor: 'bg-card/80',  // 硬编码的背景色
    statusDotSize: 'w-1 h-1',
    borderRadius: 'rounded-md',
    boxShadow: 'shadow-sm',
    vehiclesPerRow: 4,
    gap: 0,
    cardSize: 'small',
  }
}
```

由于数据传递链断裂，`settings.inTaskVehicleCardStyles` 总是 undefined，导致总是使用硬编码的fallback对象。

## 解决方案

### 1. 修复数据传递链

#### 1.1 更新TaskListBody接口定义

在 `task-list-body.tsx` 中添加 `inTaskVehicleCardStyles` 到 settings 类型：

```typescript
settings: {
  columnWidths: ColumnSizingState;
  columnOrder: ColumnOrderState;
  columnVisibility: VisibilityState;
  enableZebraStriping: boolean;
  groupConfig: TaskGroupConfig;
  inTaskVehicleCardStyles?: any; // 添加车辆卡片样式设置
};
```

#### 1.2 在TaskListContainer中传递样式设置

修改 `TaskListContainer.tsx`，在 settings 对象中包含 `inTaskVehicleCardStyles`：

```typescript
settings={{
  columnWidths: taskListSettings.settings.columnWidths,
  columnOrder: taskListSettings.settings.columnOrder,
  columnVisibility: taskListSettings.settings.columnVisibility,
  enableZebraStriping: taskListSettings.settings.enableZebraStriping,
  groupConfig: taskListSettings.settings.groupConfig,
  inTaskVehicleCardStyles: taskListSettings.settings.inTaskVehicleCardStyles, // 添加这一行
}}
```

### 2. 移除硬编码的fallback对象

修改 `task-list-content-renderer.tsx`，直接传递用户的样式设置：

```typescript
// 修复后的代码
inTaskVehicleCardStyles={settings.inTaskVehicleCardStyles}
```

### 3. 在组件内部提供fallback

修改 `TaskListStackedContent` 组件，使其能够处理undefined的样式设置：

```typescript
// 在TaskListStackedContent组件内部
const defaultInTaskVehicleCardStyles: InTaskVehicleCardStyle = {
  cardWidth: 'w-14',
  cardHeight: 'h-8',
  fontSize: 'text-[12px]',
  fontColor: 'text-foreground',
  vehicleNumberFontWeight: 'font-medium',
  cardBgColor: 'bg-card/80',
  statusDotSize: 'w-1 h-1',
  borderRadius: 'rounded-md',
  boxShadow: 'shadow-sm',
  vehiclesPerRow: 4,
  gap: 0,
  cardSize: 'small',
};

const effectiveInTaskVehicleCardStyles = inTaskVehicleCardStyles || defaultInTaskVehicleCardStyles;
```

### 4. 更新类型定义

将 `inTaskVehicleCardStyles` 参数设为可选：

```typescript
interface TaskListStackedContentProps {
  // ...其他属性
  inTaskVehicleCardStyles?: InTaskVehicleCardStyle;
  // ...
}
```

## 修复的文件

1. **src/features/task-management/components/task-list-body.tsx**
   - 更新 `TaskListBodyProps` 接口，在 settings 中添加 `inTaskVehicleCardStyles?: any`

2. **src/features/task-management/components/TaskListContainer.tsx**
   - 在传递给 TaskListBody 的 settings 对象中添加 `inTaskVehicleCardStyles`

3. **src/features/task-management/components/task-list-content-renderer.tsx**
   - 移除硬编码的fallback对象
   - 直接传递用户的样式设置

4. **src/features/task-management/components/components/task-list-stacked-content.tsx**
   - 更新接口定义，使 `inTaskVehicleCardStyles` 可选
   - 在组件内部提供默认值
   - 使用 `effectiveInTaskVehicleCardStyles` 替换所有引用

## 验证方法

1. 打开车辆卡片样式编辑器
2. 修改车辆卡片的背景色、大小等样式
3. 切换到stacked模式
4. 确认车辆卡片样式与设置一致

## 影响范围

- ✅ **Stacked模式**：现在正确应用用户的车辆卡片样式设置
- ✅ **Table模式**：不受影响，继续正常工作
- ✅ **Card模式**：不受影响，继续正常工作

## 注意事项

- 修复后，stacked模式下的车辆卡片样式将与其他模式保持一致
- 如果用户没有配置样式设置，将使用合理的默认值
- 不会影响现有的功能和性能
