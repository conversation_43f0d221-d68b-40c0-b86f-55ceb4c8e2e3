// src/components/sections/task-list/hooks/use-task-list-data.ts
import { useMemo } from 'react';

import { shallow } from 'zustand/shallow';

import { useFilteredTasks } from '@/features/task-management/hooks/useFilteredTasks';
import { useGroupingStore } from '@/features/task-management/store/groupingStore';
import { useAppStore } from '@/infrastructure/storage/stores/appStore';
import type { Task, TaskGroup, Vehicle, TaskListStoredSettings } from '@/core/types';
import { groupTasks } from '@/core/utils/task-grouping';

/**
 * 任务列表数据处理Hook
 * 负责处理任务数据的获取、过滤、分组等逻辑
 */
export function useTaskListData(settings: TaskListStoredSettings) {
  // 基础数据
  const allTasks = useAppStore(state => state.tasks, shallow);
  const allVehicles = useAppStore(state => state.vehicles, shallow);
  const filteredTasks = useFilteredTasks();
  const { groupConfig } = useGroupingStore();

  // 优化：预先计算每个任务的车辆列表
  const tasksWithVehicles = useMemo(() => {
    const vehiclesByTask = new Map<string, Vehicle[]>();
    const validVehicles = Array.isArray(allVehicles) ? allVehicles : [];
    const validTasks = Array.isArray(filteredTasks) ? filteredTasks : [];

    console.log('🚗 任务车辆映射计算开始:', {
      vehicleCount: validVehicles.length,
      taskCount: validTasks.length,
      timestamp: new Date().toLocaleTimeString(),
    });

    validVehicles.forEach(vehicle => {
      if (vehicle && vehicle.assignedTaskId) {
        if (!vehiclesByTask.has(vehicle.assignedTaskId)) {
          vehiclesByTask.set(vehicle.assignedTaskId, []);
        }
        vehiclesByTask.get(vehicle.assignedTaskId)!.push(vehicle);

        console.log('🔗 车辆分配到任务:', {
          vehicleId: vehicle.id,
          vehicleNumber: vehicle.vehicleNumber,
          assignedTaskId: vehicle.assignedTaskId,
          status: vehicle.status,
        });
      } else {
        console.log('⚠️ 车辆未分配到任务:', {
          vehicleId: vehicle?.id,
          vehicleNumber: vehicle?.vehicleNumber,
          assignedTaskId: vehicle?.assignedTaskId,
          status: vehicle?.status,
          hasVehicle: !!vehicle,
        });
      }
    });

    const result = validTasks.map(task => {
      const taskVehicles = task && task.id ? vehiclesByTask.get(task.id) || [] : [];

      if (taskVehicles.length > 0) {
        console.log('📋 任务关联车辆:', {
          taskId: task.id,
          taskNumber: task.taskNumber,
          vehicleCount: taskVehicles.length,
          vehicles: taskVehicles.map(v => ({
            id: v.id,
            vehicleNumber: v.vehicleNumber,
            status: v.status,
          })),
        });
      }

      return {
        ...task,
        vehicles: taskVehicles,
      };
    });

    console.log('🎯 任务车辆映射完成:', {
      totalTasks: result.length,
      tasksWithVehicles: result.filter(t => t.vehicles && t.vehicles.length > 0).length,
    });

    return result;
  }, [filteredTasks, allVehicles]);

  // 分组数据计算 - 使用 Zustand 分组状态
  const taskGroups = useMemo(() => {
    console.log('🔍 taskGroups 重新计算 (Zustand):', {
      enabled: groupConfig.enabled,
      groupBy: groupConfig.groupBy,
      tasksCount: tasksWithVehicles.length,
      timestamp: new Date().toLocaleTimeString(),
    });

    const result = groupTasks(tasksWithVehicles, groupConfig);

    console.log(
      '🔍 分组结果 (Zustand):',
      result.map(g => ({
        key: g.key,
        label: g.label,
        taskCount: g.tasks.length,
      }))
    );

    return result;
  }, [
    tasksWithVehicles,
    groupConfig.enabled,
    groupConfig.groupBy,
    groupConfig.defaultCollapsed,
    groupConfig.sortOrder,
  ]);

  // 展开的任务列表（用于表格显示）
  const expandedTasks = useMemo(() => {
    if (!settings.groupConfig.enabled) {
      return tasksWithVehicles;
    }
    return taskGroups.filter(group => !group.collapsed).flatMap(group => group.tasks);
  }, [taskGroups, tasksWithVehicles, settings.groupConfig.enabled]);

  // 分组统计
  const groupStats = useMemo(() => {
    if (!settings.groupConfig.enabled) {
      return null;
    }

    return {
      totalGroups: taskGroups.length,
      collapsedGroups: taskGroups.filter(g => g.collapsed).length,
      expandedGroups: taskGroups.filter(g => !g.collapsed).length,
      totalTasks: taskGroups.reduce((sum, g) => sum + g.tasks.length, 0),
      visibleTasks: expandedTasks.length,
    };
  }, [taskGroups, expandedTasks, settings.groupConfig.enabled]);

  // 车辆统计
  const vehicleStats = useMemo(() => {
    const assignedVehicles = allVehicles.filter(v => v.assignedTaskId);
    const availableVehicles = allVehicles.filter(v => !v.assignedTaskId);

    return {
      total: allVehicles.length,
      assigned: assignedVehicles.length,
      available: availableVehicles.length,
      assignedVehicles,
      availableVehicles,
    };
  }, [allVehicles]);

  // 任务统计
  const taskStats = useMemo(() => {
    const stats = {
      total: filteredTasks.length,
      new: 0,
      readyToProduce: 0,
      ratioSet: 0,
      inProgress: 0,
      paused: 0,
      completed: 0,
      cancelled: 0,
    };

    filteredTasks.forEach(task => {
      switch (task.dispatchStatus) {
        case 'New':
          stats.new++;
          break;
        case 'ReadyToProduce':
          stats.readyToProduce++;
          break;
        case 'RatioSet':
          stats.ratioSet++;
          break;
        case 'InProgress':
          stats.inProgress++;
          break;
        case 'Paused':
          stats.paused++;
          break;
        case 'Completed':
          stats.completed++;
          break;
        case 'Cancelled':
          stats.cancelled++;
          break;
      }
    });

    return stats;
  }, [filteredTasks]);

  return {
    // 原始数据
    allTasks,
    allVehicles,
    filteredTasks,

    // 处理后的数据
    tasksWithVehicles,
    taskGroups,
    expandedTasks,

    // 统计数据
    groupStats,
    vehicleStats,
    taskStats,

    // 便捷方法
    getTaskById: (id: string) => allTasks.find(t => t.id === id),
    getVehicleById: (id: string) => allVehicles.find(v => v.id === id),
    getTaskVehicles: (taskId: string) => allVehicles.filter(v => v.assignedTaskId === taskId),
  };
}
