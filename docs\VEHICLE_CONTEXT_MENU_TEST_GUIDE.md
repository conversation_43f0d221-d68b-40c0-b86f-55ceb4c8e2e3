# 车辆右键菜单测试指南

## 测试前准备

1. 启动开发服务器：`yarn dev`
2. 打开浏览器开发者工具的控制台，查看日志输出
3. 确保任务列表中有已调度的车辆

## 测试步骤

### 1. Table模式测试

1. 切换到Table模式
2. 找到有调度车辆的任务行
3. 在"调度车辆"列中的车辆卡片上右键点击
4. 验证菜单是否正确显示，包含以下项目：
   - ✅ 打印发货单
   - ✅ 查看发货单详情  
   - ✅ 设定发车时间
   - ✅ 背砂浆
   - ✅ 车辆信息
   - ✅ 修改车号
   - ✅ 转发搅拌站
   - ✅ 查看本车配比
   - ✅ 设置车辆显示
   - ✅ 取消调度

### 2. Card模式测试

1. 切换到Card模式
2. 找到有调度车辆的任务卡片
3. 在任务卡片中的车辆区域的车辆卡片上右键点击
4. 验证菜单显示和功能与Table模式一致

### 3. Stacked模式测试

1. 切换到Stacked模式
2. 找到有调度车辆的任务
3. 在第二行（堆叠行）的调度车辆区域的车辆卡片上右键点击
4. 验证菜单显示和功能与其他模式一致

## 功能测试

### 基础功能测试

对每个菜单项进行功能测试：

#### 1. 打印发货单
- **操作**：点击"打印发货单"
- **预期结果**：
  - 控制台显示：`打印发货单: [车辆对象] [任务对象]`
  - 弹出新窗口显示发货单内容
  - 自动调用浏览器打印对话框

#### 2. 设定发车时间
- **操作**：点击"设定发车时间"
- **预期结果**：
  - 控制台显示：`设定发车时间: [车辆对象] [任务对象]`
  - 弹出时间输入对话框
  - 输入时间后控制台显示设定结果

#### 3. 背砂浆
- **操作**：点击"背砂浆"
- **预期结果**：
  - 控制台显示：`背砂浆: [车辆对象] [任务对象]`
  - 弹出确认对话框
  - 确认后控制台显示申请结果

#### 4. 车辆信息
- **操作**：点击"车辆信息"
- **预期结果**：
  - 控制台显示：`显示车辆信息: [车辆对象] [任务对象]`
  - 弹出车辆信息对话框，显示详细信息

#### 5. 修改车号
- **操作**：点击"修改车号"
- **预期结果**：
  - 控制台显示：`修改车号: [车辆对象] [任务对象]`
  - 弹出车号输入对话框
  - 输入新车号后控制台显示修改结果

#### 6. 转发搅拌站
- **操作**：点击"转发搅拌站"
- **预期结果**：
  - 控制台显示：`转发搅拌站: [车辆对象] [任务对象]`
  - 弹出搅拌站名称输入对话框
  - 输入后控制台显示转发结果

#### 7. 查看本车配比
- **操作**：点击"查看本车配比"
- **预期结果**：
  - 控制台显示：`查看本车配比: [车辆对象] [任务对象]`
  - 弹出配比信息对话框

#### 8. 设置车辆显示
- **操作**：点击"设置车辆显示"
- **预期结果**：
  - 控制台显示：`设置车辆显示: [车辆对象] [任务对象]`
  - 弹出样式设置提示对话框

#### 9. 查看发货单详情
- **操作**：点击"查看发货单详情"
- **预期结果**：
  - 控制台显示：`打开发货单详情: [车辆对象] [任务对象]`

#### 10. 取消调度
- **操作**：点击"取消调度"
- **预期结果**：
  - 控制台显示：`取消车辆调度: [车辆ID]`

## 边界情况测试

### 1. 无车辆情况
- 在没有调度车辆的任务上测试
- 确保不会出现错误

### 2. 多车辆情况
- 在有多辆调度车辆的任务上测试
- 确保每辆车的右键菜单都能正常工作

### 3. 不同车辆状态
- 测试不同状态的车辆（pending、outbound、returned等）
- 确保菜单功能适应不同状态

## 用户体验测试

### 1. 菜单显示
- ✅ 菜单位置正确（鼠标点击位置附近）
- ✅ 菜单大小合适，不会超出屏幕
- ✅ 菜单项分组清晰，有分隔线
- ✅ 图标显示正确

### 2. 交互体验
- ✅ 右键点击响应迅速
- ✅ 菜单项悬停效果正常
- ✅ 点击菜单项后菜单正确关闭
- ✅ 点击菜单外区域菜单正确关闭

### 3. 键盘导航
- ✅ 可以使用Tab键在菜单项间导航
- ✅ 可以使用Enter键激活菜单项
- ✅ 可以使用Escape键关闭菜单

## 浏览器兼容性测试

在以下浏览器中测试：
- ✅ Chrome (最新版本)
- ✅ Firefox (最新版本)
- ✅ Safari (最新版本)
- ✅ Edge (最新版本)

## 性能测试

### 1. 菜单响应时间
- 右键点击到菜单显示的时间应小于100ms
- 菜单项点击响应时间应小于50ms

### 2. 内存使用
- 频繁打开关闭菜单不应导致内存泄漏
- 检查开发者工具的内存使用情况

## 问题排查

### 常见问题

1. **菜单不显示**
   - 检查车辆卡片是否有 `data-vehicle-card="true"` 属性
   - 检查 `onOpenVehicleCardContextMenu` 是否正确传递

2. **菜单项不响应**
   - 检查控制台是否有错误信息
   - 确认回调函数是否正确实现

3. **菜单位置不正确**
   - 检查鼠标事件的 `clientX` 和 `clientY` 值
   - 确认菜单定位逻辑

### 调试技巧

1. 打开浏览器开发者工具
2. 在控制台中查看日志输出
3. 使用React DevTools检查组件状态
4. 检查网络请求（如果有后端交互）

## 测试报告模板

```
测试日期：[日期]
测试人员：[姓名]
浏览器：[浏览器名称和版本]

Table模式：✅/❌
Card模式：✅/❌  
Stacked模式：✅/❌

功能测试：
- 打印发货单：✅/❌
- 设定发车时间：✅/❌
- 背砂浆：✅/❌
- 车辆信息：✅/❌
- 修改车号：✅/❌
- 转发搅拌站：✅/❌
- 查看本车配比：✅/❌
- 设置车辆显示：✅/❌
- 查看发货单详情：✅/❌
- 取消调度：✅/❌

发现的问题：
[列出发现的问题]

建议改进：
[列出改进建议]
```
