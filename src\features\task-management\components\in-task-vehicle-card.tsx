// src/components/sections/task-list/in-task-vehicle-card.tsx
'use client';

import React, { useCallback, useMemo } from 'react';

import { PauseCircleIcon, PlayCircle, RotateCcw, XCircleIcon } from 'lucide-react';

import { getVehicleCardBackgroundStyle } from '@/models/vehicle-card-background-settings';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shared/components/tooltip';
import { cn, getProductionStatusColor, getProductionStatusTooltip } from '@/core/lib/utils';
import type { InTaskVehicleCardProps, Vehicle } from '@/core/types';

// src/components/sections/task-list/in-task-vehicle-card.tsx

interface CornerRibbonIndicatorProps {
  vehicle: Vehicle;
  isDispatchPanelView?: boolean;
}

/**
 * 角标指示器组件 - 优化版
 * 使用小型彩色圆点+微型图标的组合方式显示车辆状态
 * 更加节省空间且视觉清晰度高
 */
const CornerRibbonIndicator: React.FC<CornerRibbonIndicatorProps> = ({
  vehicle,
  isDispatchPanelView,
}) => {
  // 定义角标状态
  let indicatorType:
    | 'paused'
    | 'deactivated'
    | 'production-line'
    | 'return-trip'
    | 'outbound-trip'
    | null = null;
  let tooltipText = '';
  let productionLineId = '';

  // 确定角标类型
  if (vehicle.operationalStatus === 'paused') {
    indicatorType = 'paused';
    tooltipText = '已暂停';
  } else if (vehicle.operationalStatus === 'deactivated') {
    indicatorType = 'deactivated';
    tooltipText = '已停用';
  } else if (vehicle.assignedProductionLineId && !isDispatchPanelView) {
    indicatorType = 'production-line';
    productionLineId = vehicle.assignedProductionLineId;
    tooltipText = `生产线: ${productionLineId}`;
  } else if (vehicle.status === 'outbound' && vehicle.currentTripType === 'returnLeg') {
    indicatorType = 'return-trip';
    tooltipText = '往返行程：返回中';
  } else if (
    vehicle.status === 'outbound' &&
    (!vehicle.currentTripType || vehicle.currentTripType === 'outboundLeg')
  ) {
    indicatorType = 'outbound-trip';
    tooltipText = '往返行程：出发中';
  } else {
    return null;
  }

  // 渲染角标
  return (
    <TooltipProvider delayDuration={100}>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className='absolute top-0 right-0 z-10 pointer-events-none'>
            {indicatorType === 'paused' && (
              <div className='flex items-center justify-center w-3.5 h-3.5 rounded-bl-md bg-sky-600 shadow-sm'>
                <PauseCircleIcon className='h-3 w-3 font-bold  text-accent-foreground' />
              </div>
            )}

            {indicatorType === 'deactivated' && (
              <div className='flex items-center justify-center w-3.5 h-3.5 rounded-bl-md bg-teal-600 shadow-sm'>
                <XCircleIcon className='h-3 w-3 font-bold  text-accent-foreground ' />
              </div>
            )}

            {indicatorType === 'production-line' && (
              <div className='flex items-center justify-center w-3.5 h-3.5 rounded-bl-md bg-neutral-600 shadow-sm'>
                <span className='text-[9px] font-bold text-accent-foreground'>
                  {productionLineId}
                </span>
              </div>
            )}

            {indicatorType === 'return-trip' && (
              <div className='flex items-center justify-center w-3.5 h-3.5 rounded-bl-md bg-blue-100 shadow-sm'>
                <RotateCcw className='h-3 w-3 text-blue-600' />
              </div>
            )}

            {indicatorType === 'outbound-trip' && (
              <div className='flex items-center justify-center w-3.5 h-3.5 rounded-bl-md bg-green-100 shadow-sm'>
                <PlayCircle className='h-3 w-3 text-green-600' />
              </div>
            )}
          </div>
        </TooltipTrigger>
        <TooltipContent side='top' align='end' className='text-xs'>
          <p>{tooltipText}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export const InTaskVehicleCard = React.memo(
  ({
    vehicle,
    task,
    vehicleDisplayMode,
    inTaskVehicleCardStyles,
    productionLineCount: _,
    onCancelDispatch: __,
    onOpenStyleEditor: ___,
    onOpenDeliveryOrderDetails: ____,
    onOpenContextMenu,
    density = 'normal',
    attributes,
    listeners,
    setNodeRef,
    style,
    isDragging,
    isOverlay,
    isDispatchPanelView = false,
    isTableCellView = false, // Added prop with default
  }: InTaskVehicleCardProps) => {
    const cardStyles = useMemo(() => {
      const cardWidthClass = inTaskVehicleCardStyles.cardWidth || 'w-14';
      const cardHeightClass = inTaskVehicleCardStyles.cardHeight || 'h-8';
      const statusDotSizeClass = inTaskVehicleCardStyles.statusDotSize || 'w-1 h-1';

      let cardInternalPadding = 'p-0.5';
      if (density === 'compact') cardInternalPadding = 'p-px';
      else if (density === 'loose') cardInternalPadding = 'p-1';

      return {
        cardWidthClass,
        cardHeightClass,
        statusDotSizeClass,
        cardInternalPadding,
      };
    }, [inTaskVehicleCardStyles, density]);

    const handleLocalContextMenu = useCallback(
      (event: React.MouseEvent) => {
        console.log('🚗 车辆右键菜单被触发:', vehicle.id, vehicle.vehicleNumber);
        event.preventDefault();
        event.stopPropagation();
        if (onOpenContextMenu) {
          console.log('🚗 调用车辆右键菜单处理器');
          onOpenContextMenu(event, vehicle, task);
        } else {
          console.log('❌ 车辆右键菜单处理器未定义');
        }
      },
      [onOpenContextMenu, vehicle, task]
    );

    const vehicleIdentifier = useMemo(
      () => (vehicleDisplayMode === 'licensePlate' ? vehicle.vehicleNumber : vehicle.id),
      [vehicleDisplayMode, vehicle.vehicleNumber, vehicle.id]
    );

    const currentProductionStatus = useMemo(
      () => vehicle.productionStatus || (vehicle.status === 'outbound' ? 'shipped' : undefined),
      [vehicle.productionStatus, vehicle.status]
    );

    const tooltipContent = useMemo(() => {
      const tripTypeText =
        vehicle.currentTripType === 'returnLeg'
          ? '返回中'
          : vehicle.currentTripType === 'outboundLeg'
            ? '出发中'
            : '未知';
      return `车辆: ${vehicle.vehicleNumber} (${vehicle.id})\n状态: ${vehicle.status}, 操作状态: ${vehicle.operationalStatus || 'normal'}\n生产状态: ${getProductionStatusTooltip(currentProductionStatus)}\n往返状态: ${vehicle.status === 'outbound' ? tripTypeText : 'N/A'}\n产线: ${vehicle.assignedProductionLineId || 'N/A'}\n允许磅房修改: ${vehicle.allowWeighRoomEdit ? '是' : '否'}\n上次洗泵水: ${vehicle.lastTripWashedWithPumpWater ? '是' : '否'}\n${!isDispatchPanelView && task && !isTableCellView ? '右键操作' : ''}`;
    }, [vehicle, currentProductionStatus, isDispatchPanelView, task, isTableCellView]);

    const dndStyle = {
      ...style,
      cursor: listeners ? (isDragging || isOverlay ? 'grabbing' : 'grab') : 'default',
      // 移除内联样式，使用CSS类来处理拖拽效果
    };

    const statusDotBaseClass = 'rounded-full shadow-inner';
    const statusDot3DEffect =
      'relative after:absolute after:inset-0 after:rounded-full after:bg-gradient-to-br after:from-white/30 after:to-transparent after:opacity-50 before:absolute before:-inset-px before:rounded-full before:border before:border-black/10';

    const effectiveCardBgAndShadow = useMemo(() => {
      let shadowClass = inTaskVehicleCardStyles.boxShadow || 'shadow-sm';
      let bgColor = '';
      let customStyle: React.CSSProperties | undefined = undefined;
      const isSchedulableInPanel =
        isDispatchPanelView &&
        (vehicle.status === 'pending' || vehicle.status === 'returned') &&
        (vehicle.operationalStatus === 'normal' || !vehicle.operationalStatus);

      if (vehicle.lastTripWashedWithPumpWater) {
        bgColor = 'bg-red-400 dark:bg-red-500/90'; // Kept this specific red
        shadowClass = 'shadow-lg shadow-red-500/70 dark:shadow-red-400/60';
      } else if (
        isSchedulableInPanel ||
        vehicle.operationalStatus === 'paused' ||
        vehicle.operationalStatus === 'deactivated'
      ) {
        bgColor = 'bg-pink-100/90 dark:bg-pink-900/50';
        shadowClass = 'shadow-lg shadow-pink-500/30 dark:shadow-pink-700/30';
      } else {
        // 使用新的背景设置组件
        const backgroundStyle = getVehicleCardBackgroundStyle(inTaskVehicleCardStyles);
        if (backgroundStyle.style) {
          customStyle = backgroundStyle.style;
          bgColor = ''; // 清空背景类，使用内联样式
        } else {
          bgColor = backgroundStyle.className;
        }
      }

      return { bgColor, shadowClass, customStyle };
    }, [inTaskVehicleCardStyles, vehicle, isDispatchPanelView]);

    const containerClassName = useMemo(
      () =>
        cn(
          'vehicle-card flex flex-col justify-between border relative flex-shrink-0 overflow-hidden',
          effectiveCardBgAndShadow.shadowClass,
          cardStyles.cardInternalPadding,
          cardStyles.cardWidthClass,
          cardStyles.cardHeightClass,
          effectiveCardBgAndShadow.bgColor,
          inTaskVehicleCardStyles.borderRadius,
          isDragging && 'vehicle-card-dragging',
          !isOverlay && !isDragging && 'hover:scale-102 hover:shadow-md'
        ),
      [
        cardStyles,
        inTaskVehicleCardStyles.borderRadius,
        isOverlay,
        effectiveCardBgAndShadow,
        isDragging,
      ]
    );

    const showTopDots = !isDispatchPanelView;
    const showContextMenuButton = !isOverlay && task && !isTableCellView; // 右键菜单按钮的显示逻辑，在非覆盖、任务存在且非表格单元格视图时显示

    return (
      <div
        ref={setNodeRef}
        className={containerClassName}
        style={{
          ...dndStyle,
          ...effectiveCardBgAndShadow.customStyle,
        }}
        {...attributes}
        {...listeners}
        onContextMenu={showContextMenuButton ? handleLocalContextMenu : e => e.preventDefault()} // 右键菜单事件处理器，非菜单区域也禁用默认右键菜单
        title={tooltipContent}
        data-vehicle-card='true'
      >
        <CornerRibbonIndicator vehicle={vehicle} isDispatchPanelView={isDispatchPanelView} />

        <div
          className={cn('flex items-start justify-between w-full', showTopDots && 'px-0.5 pt-0.5')}
        >
          {showTopDots ? (
            <div className='flex items-center space-x-0.5'>
              <TooltipProvider delayDuration={300}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div
                      className={cn(
                        statusDotBaseClass,
                        cardStyles.statusDotSizeClass,
                        getProductionStatusColor(currentProductionStatus),
                        statusDot3DEffect
                      )}
                    />
                  </TooltipTrigger>
                  <TooltipContent side='top' align='start'>
                    <p className='text-xs'>
                      生产: {getProductionStatusTooltip(currentProductionStatus)}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              {vehicle.allowWeighRoomEdit && (
                <TooltipProvider delayDuration={300}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div
                        className={cn(
                          statusDotBaseClass,
                          cardStyles.statusDotSizeClass,
                          'bg-teal-500',
                          statusDot3DEffect
                        )}
                      />
                    </TooltipTrigger>
                    <TooltipContent side='top' align='start'>
                      <p className='text-xs'>允许磅房修改</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          ) : (
            <div className={cn(cardStyles.statusDotSizeClass, 'h-0')}></div>
          )}
        </div>

        <div className='flex-1 flex items-center justify-center px-0.5 overflow-hidden min-w-0 w-full'>
          <span
            className={cn(
              'truncate w-full text-center',
              inTaskVehicleCardStyles.fontSize,
              inTaskVehicleCardStyles.fontColor || 'text-foreground',
              inTaskVehicleCardStyles.vehicleNumberFontWeight
            )}
          >
            {vehicleIdentifier}
          </span>
        </div>
        <div className={cn(cardStyles.statusDotSizeClass, 'h-0', showTopDots && 'mb-0.5')}></div>
      </div>
    );
  },
  (prevProps, nextProps) => {
    return (
      prevProps.vehicle.id === nextProps.vehicle.id &&
      prevProps.vehicle.status === nextProps.vehicle.status &&
      prevProps.vehicle.operationalStatus === nextProps.vehicle.operationalStatus &&
      prevProps.vehicle.productionStatus === nextProps.vehicle.productionStatus &&
      prevProps.vehicle.assignedProductionLineId === nextProps.vehicle.assignedProductionLineId &&
      prevProps.vehicle.allowWeighRoomEdit === nextProps.vehicle.allowWeighRoomEdit &&
      prevProps.vehicle.lastTripWashedWithPumpWater ===
      nextProps.vehicle.lastTripWashedWithPumpWater &&
      prevProps.vehicle.currentTripType === nextProps.vehicle.currentTripType && // 添加往返状态比较
      prevProps.task?.id === nextProps.task?.id &&
      prevProps.vehicleDisplayMode === nextProps.vehicleDisplayMode &&
      prevProps.density === nextProps.density &&
      JSON.stringify(prevProps.inTaskVehicleCardStyles || {}) ===
      JSON.stringify(nextProps.inTaskVehicleCardStyles || {}) &&
      prevProps.isDragging === nextProps.isDragging &&
      prevProps.isOverlay === nextProps.isOverlay &&
      prevProps.isDispatchPanelView === nextProps.isDispatchPanelView &&
      prevProps.isTableCellView === nextProps.isTableCellView
    );
  }
);
InTaskVehicleCard.displayName = 'InTaskVehicleCard';
