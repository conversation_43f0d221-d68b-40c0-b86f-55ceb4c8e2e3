// src/components/ui/virtualized-table.tsx
import React, { useMemo, useCallback, useRef } from 'react';

import { useTableScrollState } from '@/shared/hooks/useTableScrollState';

import {
  ColumnDef,
  ColumnOrderState,
  ColumnSizingState,
  flexRender,
  getCoreRowModel,
  Header,
  OnChangeFn,
  Table as ReactTableType,
  Row,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table';
import { useVirtualizer } from '@tanstack/react-virtual';

import {
  useTaskSelectionActions,
  useTaskSelectionState,
} from '@/shared/components/contexts/TaskSelectionContext';
import { cn } from '@/core/lib/utils';
import type { CustomColumnDefinition, DensityStyleValues, Task, Vehicle } from '@/core/types';
import { ZIndexLevels } from '@/core/types/sticky-columns';

import { ColumnDragDropProvider } from './column-drag-drop-provider';
import { DraggableHeader } from './draggable-header';

export interface VirtualizedTableProps<TData extends object> {
  data: TData[];
  columns: ColumnDef<TData, any>[];
  getRowId?: (originalRow: TData, index: number, parent?: Row<TData>) => string;
  densityStyles: DensityStyleValues;
  enableZebraStriping: boolean;
  estimateRowHeightAction: (task?: TData) => number;
  totalTableWidth: number;

  columnSizing: ColumnSizingState;
  onColumnSizingChangeAction?: OnChangeFn<ColumnSizingState>;
  columnVisibility: VisibilityState;
  onColumnVisibilityChangeAction?: OnChangeFn<VisibilityState>;
  columnOrder: ColumnOrderState;
  onColumnOrderChangeAction?: OnChangeFn<ColumnOrderState>;

  onHeaderContextMenuAction?: (event: React.MouseEvent, column: CustomColumnDefinition) => void;
  onHeaderDoubleClickAction?: (event: React.MouseEvent, column: CustomColumnDefinition) => void;
  onRowContextMenuAction?: (event: React.MouseEvent, row: Row<TData>) => void;
  onRowDoubleClickAction?: (row: Row<TData>) => void;

  onDropOnProductionLineAction?: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  getColumnBackgroundPropsAction?: (
    columnId: string,
    isHeader: boolean,
    isFixed: boolean
  ) => { style: React.CSSProperties; className: string };
  getCellTextClassesAction?: (columnId: string) => string;
  isGroupedMode?: boolean; // 新增：是否为分组模式
}

// DraggableHeader component and related functionality has been moved to separate files
// See ./draggable-header.tsx and ./column-drag-drop-provider.tsx
// This improves code organization and maintainability

const TableRowWrapper = React.memo(
  ({
    row,
    children,
    estimateRowHeight,
    getRowClassName,
    index,
  }: {
    row: Row<any>;
    children: React.ReactNode;
    estimateRowHeight: () => number;
    getRowClassName: (row: Row<any>, index: number) => string;
    index: number;
  }) => {
    const task = row.original as Task;
    const { setSelectedTask } = useTaskSelectionActions();
    const { isTaskSelected } = useTaskSelectionState();

    // 先计算选中状态
    const isSelected = task ? isTaskSelected(task.id) : false;

    const handleRowClick = useCallback(
      (e: React.MouseEvent) => {
        // 避免在点击按钮或其他交互元素时触发行选中
        const target = e.target as HTMLElement;
        if (target.closest('button, a, input, select, textarea, [role="button"]')) {
          return;
        }

        // 切换选中状态：如果已选中则取消选中，否则选中
        if (isSelected) {
          setSelectedTask(null);
        } else {
          setSelectedTask(task);
        }
      },
      [task, setSelectedTask, isSelected]
    );

    return (
      <tr
        key={row.id}
        data-index={index}
        data-task-id={task?.id}
        data-row-id={task?.id}
        onClick={handleRowClick}
        className={cn(
          getRowClassName(row, index),
          'virtual-row-performance', // Ensure this class applies `contain: layout style;`
          'task-row-clickable', // 添加点击样式
          isSelected && 'task-row-selected' // 添加选中样式
        )}
        style={{ height: `${estimateRowHeight()}px` }}
      >
        {children}
      </tr>
    );
  }
);
TableRowWrapper.displayName = 'TableRowWrapper';

export function VirtualizedTable<TData extends object>({
  data,
  columns,
  getRowId,
  densityStyles,
  enableZebraStriping,
  estimateRowHeightAction,
  totalTableWidth,
  columnSizing,
  onColumnSizingChangeAction,
  columnVisibility,
  onColumnVisibilityChangeAction,
  columnOrder,
  onColumnOrderChangeAction,
  onHeaderContextMenuAction,
  onHeaderDoubleClickAction,
  onRowContextMenuAction,
  onRowDoubleClickAction,
  onDropOnProductionLineAction,
  getColumnBackgroundPropsAction,
  getCellTextClassesAction,
  isGroupedMode = false,
}: VirtualizedTableProps<TData>) {
  const tableContainerRef = useRef<HTMLDivElement>(null);

  // 使用滚动状态Hook来检测表格是否滚动到最左侧
  const scrollState = useTableScrollState(tableContainerRef);

  const table = useReactTable({
    data,
    columns,
    getRowId,
    state: {
      columnSizing,
      columnVisibility,
      columnOrder,
    },
    onColumnSizingChange: onColumnSizingChangeAction,
    onColumnVisibilityChange: onColumnVisibilityChangeAction,
    onColumnOrderChange: onColumnOrderChangeAction,
    getCoreRowModel: getCoreRowModel(),
    columnResizeMode: 'onChange',
    meta: {
      densityStyles,
      onDropOnProductionLine: onDropOnProductionLineAction,
      getColumnBackgroundProps: getColumnBackgroundPropsAction,
    },
    // Force re-render when getColumnBackgroundProps changes
    autoResetAll: false,
    enableColumnResizing: true,
  });

  const { rows } = table.getRowModel();

  // 优化行高估算，使用缓存避免重复计算
  const estimateRowHeight = React.useCallback(
    (index: number) => {
      const row = rows[index];
      if (!row) return 40; // 默认行高
      return estimateRowHeightAction(row.original);
    },
    [rows, estimateRowHeightAction]
  );

  // 优化overscan值，减少不必要的渲染
  const optimizedOverscan = React.useMemo(() => {
    if (rows.length < 20) return 20;
    if (rows.length < 100) return 40;
    if (rows.length < 500) return 50;
    return 40; // 最大overscan值
  }, [rows.length]);
  // 将 useMemo 调用移到组件顶层
  const leftFixedVisibleColumns = useMemo(() => {
    return table
      .getVisibleLeafColumns()
      .filter(col => (col.columnDef.meta as any)?.customDef?.fixed === 'left');
  }, [table]);

  const rightFixedVisibleColumns = useMemo(() => {
    return table
      .getVisibleLeafColumns()
      .filter(col => (col.columnDef.meta as any)?.customDef?.fixed === 'right');
  }, [table]);
  const rowVirtualizer = useVirtualizer({
    count: rows.length,
    getScrollElement: () => tableContainerRef.current,
    estimateSize: estimateRowHeight,
    overscan: optimizedOverscan,
    scrollMargin: 0,
    initialOffset: 0,
    // 添加滚动性能优化 - 移除measureElement以使用默认行为
    measureElement: undefined,
  });

  const virtualRows = rowVirtualizer.getVirtualItems();
  const virtualizerTotalSize = rowVirtualizer.getTotalSize();
  const paddingTop = virtualRows.length > 0 ? virtualRows[0]?.start || 0 : 0;
  const paddingBottom =
    virtualRows.length > 0
      ? virtualizerTotalSize - (virtualRows[virtualRows.length - 1]?.end || 0)
      : 0;

  const headerGroups = table.getHeaderGroups();

  // Column drag-drop functionality has been moved to ColumnDragDropProvider component
  // This allows for better separation of concerns and reusability

  /**
   * @function getRowClassName
   * @description Get the CSS class name for a table row based on its properties and index
   */

  const getRowClassName = React.useCallback(
    (row: Row<TData>, rowIdx: number) => {
      const classes = ['tr', rowIdx % 2 === 0 ? '' : enableZebraStriping ? 'bg-muted/30' : ''];
      if ((row.original as Task)?.isDueForDispatch === true) {
        classes.push('task-row-dispatch-due');
      }
      return classes.join(' ');
    },
    [enableZebraStriping]
  );

  return (
    <ColumnDragDropProvider
      tableInstance={table}
      initialColumnOrder={columnOrder}
      onColumnOrderChange={onColumnOrderChangeAction}
      disableDragDrop={false}
    >
      <div
        ref={tableContainerRef}
        className='custom-scrollbar virtualized-table-container'
        style={{
          height: '100%',
          minHeight: 0,
          flex: '1 1 0%',
          overflow: 'auto',
          position: 'relative',
          // 添加滚动性能优化
          willChange: 'scroll-position',
          transform: 'translateZ(0)', // 启用硬件加速
          WebkitOverflowScrolling: 'touch', // iOS 滚动优化
        }}
      >
        <table
          style={{ width: '100%', minWidth: Math.max(totalTableWidth, 800), tableLayout: 'fixed' }}
          className='virtualized-table'
        >
          <thead
            className='sticky top-0 bg-blue-100 border-b border-gray-30'
            style={{ zIndex: ZIndexLevels.TABLE_THEAD_STICKY }}
          >
            {headerGroups.map(headerGroup => (
              <tr key={headerGroup.id} className='border-b'>
                {headerGroup.headers.map((header, headerIndex) => (
                  <DraggableHeader
                    key={header.id}
                    header={header}
                    getColumnBackgroundProps={getColumnBackgroundPropsAction}
                    onHeaderContextMenu={onHeaderContextMenuAction}
                    onHeaderDoubleClick={onHeaderDoubleClickAction}
                    index={headerIndex}
                    tableInstance={table}
                    scrollState={scrollState}
                  />
                ))}
              </tr>
            ))}
          </thead>
          <tbody>
            {paddingTop > 0 && <tr style={{ height: `${paddingTop}px` }} />}
            {virtualRows.map(virtualRow => {
              const row = rows[virtualRow.index] as Row<TData>;
              return (
                <TableRowWrapper
                  key={row.id}
                  row={row}
                  estimateRowHeight={() => estimateRowHeightAction(row.original)}
                  getRowClassName={getRowClassName}
                  index={virtualRow.index}
                >
                  {row.getVisibleCells().map(cell => {
                    const customDefMeta = cell.column.columnDef.meta as
                      | {
                        customDef?: CustomColumnDefinition & {
                          densityStyles?: DensityStyleValues;
                        };
                      }
                      | undefined;
                    const customDef = customDefMeta?.customDef;

                    let finalStyle: React.CSSProperties = {
                      width: cell.column.getSize(),
                      minWidth: cell.column.columnDef.minSize,
                      maxWidth: cell.column.columnDef.maxSize,
                    };
                    let finalClassName = cn(
                      'align-middle whitespace-nowrap relative', // Added relative for pseudo-elements
                      densityStyles.cellPaddingX,
                      densityStyles.cellPaddingY
                    );

                    const visibleLeafColumns = table.getVisibleLeafColumns();
                    let isLastVisibleLeftFixedCell = false;
                    let isFirstVisibleRightFixedCell = false;

                    if (customDef?.fixed) {
                      finalStyle.position = 'sticky';
                      let offset = 0;

                      if (customDef.fixed === 'left') {
                        isLastVisibleLeftFixedCell =
                          leftFixedVisibleColumns.length > 0 &&
                          leftFixedVisibleColumns[leftFixedVisibleColumns.length - 1]?.id ===
                          cell.column.id;
                        for (const col of leftFixedVisibleColumns) {
                          if (col.id === cell.column.id) break;
                          offset += col.getSize();
                        }
                        finalStyle.left = `${offset}px`;
                      } else {
                        // 'right'
                        isFirstVisibleRightFixedCell =
                          rightFixedVisibleColumns.length > 0 &&
                          rightFixedVisibleColumns[0]?.id === cell.column.id;
                        const currentIndex = rightFixedVisibleColumns.findIndex(
                          c => c.id === cell.column.id
                        );
                        for (let i = currentIndex + 1; i < rightFixedVisibleColumns.length; i++) {
                          offset += rightFixedVisibleColumns[i]?.getSize() ?? 0;
                        }
                        finalStyle.right = `${offset}px`;
                      }

                      // Background application for fixed cells
                      const userBgProps =
                        getColumnBackgroundPropsAction && customDef.isStyleable !== false
                          ? getColumnBackgroundPropsAction(cell.column.id, false, true)
                          : { style: {}, className: '' };

                      let userHasOpaqueBg = false;
                      if (userBgProps.style.backgroundColor) {
                        finalStyle.backgroundColor = userBgProps.style.backgroundColor;
                        userHasOpaqueBg = true;
                      }
                      if (userBgProps.className) {
                        // 检查是否有任何背景色类名（包括浅底背景色）
                        const hasBgClass = userBgProps.className
                          .split(' ')
                          .some(cls => cls.startsWith('bg-') && !cls.includes('transparent'));

                        if (hasBgClass) {
                          userHasOpaqueBg = true;
                        }

                        // 应用所有背景色类名
                        finalClassName = cn(finalClassName, userBgProps.className);
                      }

                      if (!userHasOpaqueBg) {
                        // Fallback for fixed cells if user didn't provide opaque bg
                        finalStyle.backgroundColor =
                          'hsl(var(--fixed-column-cell-background-default))';
                      }

                      // Z-Index and Shadow Class application
                      if (isLastVisibleLeftFixedCell) {
                        // 只有当表格不在最左侧时才显示右侧阴影
                        if (!scrollState.isAtLeft) {
                          finalClassName = cn(finalClassName, 'sticky-shadow-caster-right');
                        }
                        finalStyle.zIndex = ZIndexLevels.STICKY_BODY;
                      } else if (isFirstVisibleRightFixedCell) {
                        // 只有当表格不在最右侧时才显示左侧阴影
                        if (!scrollState.isAtRight) {
                          finalClassName = cn(finalClassName, 'sticky-shadow-caster-left');
                        }
                        finalStyle.zIndex = ZIndexLevels.STICKY_BODY;
                      } else {
                        finalStyle.zIndex = ZIndexLevels.STICKY_BODY;
                      }
                    } else {
                      // Not fixed
                      const userBgProps =
                        getColumnBackgroundPropsAction &&
                          customDef &&
                          customDef.isStyleable !== false
                          ? getColumnBackgroundPropsAction(cell.column.id, false, false)
                          : { style: {}, className: '' };

                      if (userBgProps.style.backgroundColor) {
                        finalStyle.backgroundColor = userBgProps.style.backgroundColor;
                      }
                      finalClassName = cn(finalClassName, userBgProps.className);
                      finalStyle.zIndex = ZIndexLevels.TABLE_CELL;
                    }

                    return (
                      <td
                        key={cell.id}
                        data-column-id={cell.column.id}
                        style={finalStyle}
                        className={finalClassName}
                        title={
                          (typeof cell.getValue() === 'string' ||
                            typeof cell.getValue() === 'number') &&
                            !(
                              customDef?.id === 'dispatchedVehicles' ||
                              customDef?.id === 'productionLines'
                            )
                            ? String(cell.getValue())
                            : undefined
                        }
                        onContextMenu={
                          onRowContextMenuAction ? e => {
                            // 检查事件是否来自车辆卡片，如果是则不处理
                            const target = e.target as HTMLElement;
                            const isVehicleCard = target.closest('[data-vehicle-card="true"]');

                            if (isVehicleCard) {
                              return; // 不处理来自车辆卡片的事件
                            }

                            onRowContextMenuAction(e, row);
                          } : undefined
                        }
                        onDoubleClick={
                          onRowDoubleClickAction ? () => onRowDoubleClickAction(row) : undefined
                        }
                      >
                        {/* Inner div for content and truncation, NOT for overflow:hidden on td itself */}
                        <div
                          className={cn(
                            'truncate text-ellipsis whitespace-nowrap w-full h-full overflow-hidden',
                            // 优先使用用户自定义的文字样式，否则使用密度样式
                            getCellTextClassesAction && customDef?.isStyleable !== false
                              ? getCellTextClassesAction(cell.column.id)
                              : cn(
                                customDef?.densityStyles?.cellFontSize ||
                                densityStyles.cellFontSize,
                                customDef?.densityStyles?.cellFontWeight ||
                                densityStyles.cellFontWeight
                              )
                          )}
                        >
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </div>
                      </td>
                    );
                  })}
                </TableRowWrapper>
              );
            })}
            {paddingBottom > 0 && <tr style={{ height: `${paddingBottom}px` }} />}
          </tbody>
        </table>
      </div>
    </ColumnDragDropProvider>
  );
}
