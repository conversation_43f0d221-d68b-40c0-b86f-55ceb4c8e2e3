// src/components/sections/task-list/cells/DispatchedVehiclesCell.tsx
'use client';

import React, { memo, useMemo, useRef, useEffect } from 'react';
import { useDrop } from 'react-dnd';

import { cn } from '@/core/lib/utils';
import { ItemTypes } from '@/core/constants/dndItemTypes';
import { useTaskHighlight } from '@/shared/components/contexts/TaskRowHighlightContext';
import type {
  InTaskVehicleCardStyle,
  Task,
  TaskListDensityMode,
  Vehicle,
  VehicleDisplayMode,
} from '@/core/types';

import { DraggableTableVehicleCard } from './DraggableTableVehicleCard';

// src/components/sections/task-list/cells/DispatchedVehiclesCell.tsx

interface DispatchedVehiclesCellProps {
  task: Task;
  taskVehicles: Vehicle[];
  vehicleDisplayMode: VehicleDisplayMode;
  inTaskVehicleCardStyles: InTaskVehicleCardStyle;
  productionLineCount: number;
  density: Exclude<TaskListDensityMode, '' | 'card'>;
  onCancelDispatch: (vehicleId: string) => void;
  onOpenStyleEditor: () => void;
  onOpenDeliveryOrderDetails: (vehicle: Vehicle, task: Task) => void;
  onOpenContextMenu: (event: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
  onDropVehicleOnTask?: (vehicle: Vehicle, taskId: string, sourceTaskId?: string) => void;
}

const DispatchedVehiclesCellComponent: React.FC<DispatchedVehiclesCellProps> = ({
  task,
  taskVehicles,
  vehicleDisplayMode,
  inTaskVehicleCardStyles,
  productionLineCount,
  density,
  onCancelDispatch,
  onOpenStyleEditor,
  onOpenDeliveryOrderDetails,
  onOpenContextMenu,
  onDropVehicleOnTask,
}) => {
  // 所有hooks必须在组件顶部调用，确保调用顺序一致
  const { setHighlightedTaskId } = useTaskHighlight();
  const ref = useRef<HTMLDivElement>(null);

  // 添加拖拽目标功能，用于车辆转发到该任务
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: [ItemTypes.VEHICLE_CARD_DISPATCH, 'vehicle-card'],
    canDrop: (item: any) => {
      // 可以接收来自其他任务的车辆转发
      return task.dispatchStatus === 'InProgress';
    },
    hover: (item: any, monitor: any) => {
      // 拖拽悬停时高亮任务行
      if (monitor.isOver() && monitor.canDrop()) {
        setHighlightedTaskId(task.id);
      }
    },
    drop: (item: any) => {
      if (onDropVehicleOnTask) {
        // 处理来自车辆调度模块的直接拖拽
        if (item.type === ItemTypes.VEHICLE_CARD_DISPATCH && item.vehicle) {
          const vehicle = item.vehicle;
          const sourceTaskId = item.sourceTaskId || vehicle.assignedTaskId;

          if (sourceTaskId && sourceTaskId !== task.id) {
            // 跨任务转发
            onDropVehicleOnTask(vehicle, task.id, sourceTaskId);
          }
          setHighlightedTaskId(null);
          return;
        }

        // 处理来自统一拖拽适配器的拖拽
        if (item.data && item.data['vehicle']) {
          const vehicleData = item.data;
          const sourceTaskId =
            vehicleData['sourceTaskId'] || vehicleData['vehicle']['assignedTaskId'];

          if (sourceTaskId && sourceTaskId !== task.id) {
            // 跨任务转发
            onDropVehicleOnTask(vehicleData['vehicle'], task.id, sourceTaskId);
          }
          setHighlightedTaskId(null);
        }
      }
    },
    collect: (monitor: any) => {
      return {
        isOver: monitor.isOver(),
        canDrop: monitor.canDrop(),
      };
    },
  });

  const layoutClasses = useMemo(() => {
    switch (density) {
      case 'compact':
        return 'gap-2 py-0 px-0';
      case 'normal':
        return 'gap-1 py-0 px-0';
      case 'loose':
        return 'gap-2 py-1 px-1';
      default:
        return 'gap-1 py-0 px-0';
    }
  }, [density]);

  // 连接drop ref
  drop(ref);

  // 使用useEffect来处理拖拽状态变化，避免在render中调用setState
  useEffect(() => {
    if (!isOver) {
      setHighlightedTaskId(null);
    }
  }, [isOver, setHighlightedTaskId]);

  // 临时移除严格的状态检查，看看车辆是否真的被分配了
  if (taskVehicles.length === 0) {
    return <div className='text-center text-xs py-1 text-muted-foreground'>-</div>;
  }

  const cellClass = cn(
    'flex flex-row flex-wrap items-center overflow-x-auto overflow-y-hidden custom-thin-horizontal-scrollbar w-full h-full relative',
    layoutClasses
  );

  const isActiveDropTarget = isOver && canDrop;

  return (
    <div
      ref={ref}
      className={cn(cellClass, isActiveDropTarget && 'bg-primary/10 ring-1 ring-primary/50')}
      style={{
        maxWidth: '100%',
        boxSizing: 'border-box',
      }}
      title={
        taskVehicles.length > 0
          ? `${taskVehicles.length}辆车${inTaskVehicleCardStyles.vehiclesPerRow ? `, 每行最多显示${inTaskVehicleCardStyles.vehiclesPerRow}辆` : ''}`
          : '无调度车辆'
      }
      onContextMenu={e => {
        // 如果点击的不是车辆卡片，则阻止默认菜单并触发任务右键菜单
        const target = e.target as HTMLElement;
        if (!target.closest('[data-vehicle-card]')) {
          e.preventDefault();
          e.stopPropagation();
        }
      }}
    >
      {taskVehicles.length > 0 ? (
        <>
          <div
            className={cn(
              'flex flex-row flex-nowrap items-center h-full',
              layoutClasses.startsWith('gap-') ? layoutClasses : 'gap-1'
            )}
          >
            {taskVehicles.map((vehicle, index) => (
              <DraggableTableVehicleCard
                key={`table-cell-vehicle-${vehicle.id}-${task.id}`}
                vehicle={vehicle}
                task={task}
                index={index}
                vehicleDisplayMode={vehicleDisplayMode}
                inTaskVehicleCardStyles={inTaskVehicleCardStyles}
                productionLineCount={productionLineCount}
                density={density}
                onCancelDispatch={onCancelDispatch}
                onOpenStyleEditor={onOpenStyleEditor}
                onOpenDeliveryOrderDetails={() => onOpenDeliveryOrderDetails(vehicle, task)}
                onOpenContextMenu={e => onOpenContextMenu(e, vehicle, task)}
              />
            ))}
          </div>
        </>
      ) : (
        <span
          className='text-[10px] text-muted-foreground italic mx-auto self-center'
          onContextMenu={e => {
            e.preventDefault();
            e.stopPropagation();
          }}
        >
          无调度车辆
        </span>
      )}
    </div>
  );
};

export const DispatchedVehiclesCell = memo(DispatchedVehiclesCellComponent);
