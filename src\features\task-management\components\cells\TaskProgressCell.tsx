// src/components/sections/task-list/cells/TaskProgressCell.tsx
'use client';

import React, { memo } from 'react';

import { Progress } from '@/shared/components/progress';
import { cn } from '@/core/lib/utils';
import type { Task } from '@/core/types';

// src/components/sections/task-list/cells/TaskProgressCell.tsx

interface TaskProgressCellProps {
  progressValue: number;
  textClassName?: string;
}

const TaskProgressCellComponent: React.FC<TaskProgressCellProps> = ({
  progressValue,
  textClassName,
}) => {
  return (
    <div className='flex flex-col items-center justify-center h-full py-1'>
      <Progress value={progressValue} className='w-full h-1.5' />
      <span className={cn('text-[8px] mt-0.5 leading-none', textClassName)}>
        {Math.round(progressValue)}%
      </span>
    </div>
  );
};

export const TaskProgressCell = memo(TaskProgressCellComponent);
