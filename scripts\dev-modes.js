#!/usr/bin/env node

/**
 * 开发模式启动脚本
 * 提供不同的开发模式以优化性能和调试体验
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 颜色输出函数
function colorLog(color, message) {
  const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    magenta: '\x1b[35m',
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 预定义的开发模式
const DEV_MODES = {
  performance: {
    name: '高性能模式',
    description: '关闭所有调试输出，获得最佳性能',
    env: {
      REACT_APP_DEBUG_VEHICLES: 'false',
      REACT_APP_DEBUG_DISPATCH: 'false',
      REACT_APP_DEBUG_PERFORMANCE: 'false',
      REACT_APP_DEBUG_MODALS: 'false',
      REACT_APP_DEBUG_STORE: 'false',
      REACT_APP_DEBUG_HOOKS: 'false',
      REACT_APP_DEBUG_DEVTOOLS: 'false',
      GENERATE_SOURCEMAP: 'false',
      REACT_APP_DISABLE_PROFILER: 'true',
    },
  },
  debug: {
    name: '完整调试模式',
    description: '启用所有调试输出，用于问题排查',
    env: {
      REACT_APP_DEBUG_VEHICLES: 'true',
      REACT_APP_DEBUG_DISPATCH: 'true',
      REACT_APP_DEBUG_PERFORMANCE: 'true',
      REACT_APP_DEBUG_MODALS: 'true',
      REACT_APP_DEBUG_STORE: 'true',
      REACT_APP_DEBUG_HOOKS: 'true',
      REACT_APP_DEBUG_DEVTOOLS: 'true',
      GENERATE_SOURCEMAP: 'true',
      REACT_APP_DISABLE_PROFILER: 'false',
    },
  },
  vehicles: {
    name: '车辆调试模式',
    description: '仅启用车辆相关调试',
    env: {
      REACT_APP_DEBUG_VEHICLES: 'true',
      REACT_APP_DEBUG_DISPATCH: 'true',
      REACT_APP_DEBUG_PERFORMANCE: 'false',
      REACT_APP_DEBUG_MODALS: 'false',
      REACT_APP_DEBUG_STORE: 'false',
      REACT_APP_DEBUG_HOOKS: 'false',
      REACT_APP_DEBUG_DEVTOOLS: 'false',
      GENERATE_SOURCEMAP: 'false',
      REACT_APP_DISABLE_PROFILER: 'true',
    },
  },
  profiling: {
    name: '性能分析模式',
    description: '启用性能监控，用于性能分析',
    env: {
      REACT_APP_DEBUG_VEHICLES: 'false',
      REACT_APP_DEBUG_DISPATCH: 'false',
      REACT_APP_DEBUG_PERFORMANCE: 'true',
      REACT_APP_DEBUG_MODALS: 'false',
      REACT_APP_DEBUG_STORE: 'false',
      REACT_APP_DEBUG_HOOKS: 'false',
      REACT_APP_DEBUG_DEVTOOLS: 'false',
      GENERATE_SOURCEMAP: 'true',
      REACT_APP_DISABLE_PROFILER: 'false',
    },
  },
};

// 显示帮助信息
function showHelp() {
  colorLog('cyan', '\n🚀 开发模式启动脚本\n');
  colorLog('yellow', '用法:');
  colorLog('white', '  node scripts/dev-modes.js [模式]\n');

  colorLog('yellow', '可用模式:');
  Object.entries(DEV_MODES).forEach(([key, mode]) => {
    colorLog('green', `  ${key.padEnd(12)} - ${mode.name}`);
    colorLog('white', `  ${' '.repeat(15)} ${mode.description}\n`);
  });

  colorLog('yellow', '示例:');
  colorLog('white', '  node scripts/dev-modes.js performance  # 高性能模式');
  colorLog('white', '  node scripts/dev-modes.js debug       # 完整调试模式');
  colorLog('white', '  node scripts/dev-modes.js vehicles    # 车辆调试模式\n');
}

// 创建环境变量文件
function createEnvFile(mode) {
  const envPath = path.join(process.cwd(), '.env.development');
  const modeConfig = DEV_MODES[mode];

  if (!modeConfig) {
    colorLog('red', `❌ 未知模式: ${mode}`);
    showHelp();
    process.exit(1);
  }

  colorLog('blue', `🔧 配置 ${modeConfig.name}...`);

  // 生成环境变量内容
  const envContent = [
    '# 开发环境配置文件',
    `# 当前模式: ${modeConfig.name}`,
    `# 描述: ${modeConfig.description}`,
    '# 由 dev-modes.js 脚本自动生成',
    '',
    'NODE_ENV=development',
    '',
    '# 调试开关',
    ...Object.entries(modeConfig.env).map(([key, value]) => `${key}=${value}`),
    '',
    '# 开发服务器配置',
    'FAST_REFRESH_TIMEOUT=1000',
    'DISABLE_ESLINT_PLUGIN=false',
    '',
  ].join('\n');

  fs.writeFileSync(envPath, envContent, 'utf8');
  colorLog('green', `✅ 已创建 .env.development 文件`);
}

// 启动开发服务器
function startDevServer() {
  colorLog('blue', '🚀 启动开发服务器...\n');

  try {
    execSync('yarn dev', {
      stdio: 'inherit',
      cwd: process.cwd(),
    });
  } catch (error) {
    colorLog('red', '❌ 启动失败');
    process.exit(1);
  }
}

// 主函数
function main() {
  const args = process.argv.slice(2);

  if (args.length === 0 || args[0] === '--help' || args[0] === '-h') {
    showHelp();
    return;
  }

  const mode = args[0];

  if (!DEV_MODES[mode]) {
    colorLog('red', `❌ 未知模式: ${mode}\n`);
    showHelp();
    return;
  }

  // 创建环境变量文件
  createEnvFile(mode);

  // 启动开发服务器
  startDevServer();
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = { DEV_MODES, createEnvFile };
