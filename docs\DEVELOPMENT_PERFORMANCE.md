# 开发环境性能优化指南

## 问题描述

开发环境 (`yarn dev`) 出现卡顿严重、性能差的问题，而生产环境部署后响应很快。

## 主要原因

1. **大量Console输出**：代码中存在大量的 `console.log`、`console.warn`、`console.error` 语句
2. **开发工具开销**：React DevTools、热重载、源码映射等开发工具的性能开销
3. **调试代码**：性能监控、状态跟踪等调试代码在开发环境中持续运行

## 解决方案

### 1. 条件化Console输出

所有console输出现在都通过环境变量控制：

```typescript
// 之前：每次都输出，影响性能
console.log('🚗 列渲染获取车辆:', vehicleData);

// 现在：仅在需要时输出
if (process.env.NODE_ENV === 'development' && process.env['REACT_APP_DEBUG_VEHICLES'] === 'true') {
  console.log('🚗 列渲染获取车辆:', vehicleData);
}
```

### 2. 环境变量配置

在 `.env.development` 文件中控制调试输出：

```bash
# 默认关闭所有调试输出以提升性能
REACT_APP_DEBUG_VEHICLES=false
REACT_APP_DEBUG_DISPATCH=false
REACT_APP_DEBUG_PERFORMANCE=false
REACT_APP_DEBUG_MODALS=false
```

### 3. 性能优化配置

```bash
# 禁用源码映射（提升构建速度）
GENERATE_SOURCEMAP=false

# 减少热重载检查频率
FAST_REFRESH_TIMEOUT=1000

# 禁用React性能分析器
REACT_APP_DISABLE_PROFILER=true
```

## 使用方法

### 正常开发（高性能模式）

```bash
yarn dev
```

所有调试输出默认关闭，获得最佳性能。

### 调试特定功能

需要调试车辆相关功能时：

```bash
# 临时启用车辆调试
REACT_APP_DEBUG_VEHICLES=true yarn dev
```

或者修改 `.env.development` 文件：

```bash
REACT_APP_DEBUG_VEHICLES=true
```

### 性能分析模式

需要进行性能分析时：

```bash
REACT_APP_DEBUG_PERFORMANCE=true yarn dev
```

## 调试开关说明

| 环境变量 | 功能 | 性能影响 | 建议使用场景 |
|---------|------|----------|-------------|
| `REACT_APP_DEBUG_VEHICLES` | 车辆渲染调试 | 高 | 车辆显示问题排查 |
| `REACT_APP_DEBUG_DISPATCH` | 调度流程调试 | 中 | 车辆调度问题排查 |
| `REACT_APP_DEBUG_PERFORMANCE` | 性能监控 | 高 | 性能分析和优化 |
| `REACT_APP_DEBUG_MODALS` | 模态框调试 | 低 | 模态框状态问题 |

## 性能监控

### 检查当前性能

1. 打开浏览器开发者工具
2. 切换到 Performance 标签
3. 录制页面操作
4. 查看是否有大量的console输出影响性能

### 优化建议

1. **仅在必要时启用调试**：默认关闭所有调试输出
2. **使用性能分析工具**：React DevTools Profiler
3. **监控内存使用**：避免内存泄漏
4. **减少重新渲染**：使用React.memo、useMemo、useCallback

## 故障排除

### 如果性能仍然不佳

1. 检查是否有其他console输出未被条件化
2. 检查是否有无限循环的useEffect
3. 检查是否有大量的组件重新渲染
4. 使用React DevTools Profiler分析性能瓶颈

### 恢复默认设置

删除或重置 `.env.development` 文件：

```bash
git checkout .env.development
```

## 注意事项

1. **生产环境不受影响**：这些优化仅影响开发环境
2. **调试时记得启用**：排查问题时需要手动启用相关调试开关
3. **团队协作**：`.env.development` 文件应该被git忽略，每个开发者可以有自己的配置
