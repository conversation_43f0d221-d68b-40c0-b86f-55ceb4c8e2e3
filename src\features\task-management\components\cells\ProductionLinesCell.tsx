// src/components/sections/task-list/cells/ProductionLinesCell.tsx
'use client';

import React, { memo, useRef, useEffect } from 'react';

import { ItemTypes } from '@/core/constants/dndItemTypes';
import { useTaskHighlight } from '@/shared/components/contexts/TaskRowHighlightContext';
import { useDrop } from 'react-dnd';
import { cn } from '@/core/lib/utils';
import type { DensityStyleValues, Task, Vehicle } from '@/core/types';

// src/components/sections/task-list/cells/ProductionLinesCell.tsx

interface DraggableVehicleItem {
  vehicle: Vehicle;
  index: number;
  statusList: 'pending' | 'returned';
  type: typeof ItemTypes.VEHICLE_CARD_DISPATCH;
}

interface ProductionLineBoxProps {
  task: Task;
  lineId: string;
  densityStyles: DensityStyleValues;
  onDropVehicle: (vehicle: Vehicle, taskId: string, lineId: string, sourceTaskId?: string) => void;
}

const ProductionLineBoxComponent: React.FC<ProductionLineBoxProps> = ({
  task,
  lineId,
  densityStyles,
  onDropVehicle,
}) => {
  const { setHighlightedTaskId } = useTaskHighlight();

  const ref = useRef<HTMLDivElement>(null);

  // 使用直接的React DnD来接收车辆调度模块的拖拽
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: [ItemTypes.VEHICLE_CARD_DISPATCH, 'vehicle-card'],
    canDrop: (item: any) => {
      return task.dispatchStatus === 'InProgress';
    },
    hover: (item: any, monitor: any) => {
      // 拖拽悬停时高亮任务行
      if (monitor.isOver() && monitor.canDrop()) {
        setHighlightedTaskId(task.id);
      }
    },
    drop: (item: any) => {
      // 处理来自车辆调度模块的直接拖拽
      if (item.type === ItemTypes.VEHICLE_CARD_DISPATCH && item.vehicle) {
        const vehicle = item.vehicle;
        const sourceTaskId = item.sourceTaskId || vehicle.assignedTaskId;
        const isVehicleTransfer = item.isVehicleTransfer || false;

        if (sourceTaskId && sourceTaskId !== task.id) {
          // 跨任务转发 - 无论是否标记为转发，都按转发处理
          onDropVehicle(vehicle, task.id, lineId, sourceTaskId);
        } else {
          // 同任务内调度
          onDropVehicle(vehicle, task.id, lineId);
        }
        setHighlightedTaskId(null);
        return;
      }

      // 处理来自统一拖拽适配器的拖拽
      if (item.data && item.data['vehicle']) {
        const vehicleData = item.data;
        const sourceTaskId =
          vehicleData['sourceTaskId'] || vehicleData['vehicle']['assignedTaskId'];

        if (sourceTaskId && sourceTaskId !== task.id) {
          // 跨任务转发
          onDropVehicle(vehicleData['vehicle'], task.id, lineId, sourceTaskId);
        } else {
          // 同任务内调度
          onDropVehicle(vehicleData['vehicle'], task.id, lineId);
        }
        setHighlightedTaskId(null);
      }
    },
    collect: (monitor: any) => {
      return {
        isOver: monitor.isOver(),
        canDrop: monitor.canDrop(),
      };
    },
  });

  // 连接drop ref
  drop(ref);

  // 使用useEffect来处理拖拽状态变化，避免在render中调用setState
  useEffect(() => {
    if (!isOver) {
      setHighlightedTaskId(null);
    }
  }, [isOver, setHighlightedTaskId]);

  // 添加悬停效果处理
  const handleMouseEnter = () => {
    setHighlightedTaskId(task.id);
  };

  const handleMouseLeave = () => {
    setHighlightedTaskId(null);
  };

  const isActiveDropTarget = isOver && canDrop;

  return (
    <div
      key={`cell-${lineId}-dropzone-${task.id}`}
      ref={ref}
      data-task-id={task.id}
      data-production-line-id={lineId}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className={cn(
        'border border-dashed rounded flex items-center justify-center hover:border-accent cursor-pointer transition-all duration-150 flex-shrink-0',
        densityStyles.productionLineBoxSize,
        densityStyles.productionLineBoxFontSize,
        isActiveDropTarget
          ? 'bg-accent/30 border-2 border-dashed border-accent ring-1 ring-accent scale-105 shadow-md z-10'
          : 'border-primary/70 hover:bg-accent/10',
        task.dispatchStatus !== 'InProgress' && 'opacity-50 cursor-not-allowed'
      )}
      title={
        task.dispatchStatus === 'InProgress'
          ? `生产线 ${lineId} - 调度至任务 ${task.taskNumber}`
          : `任务状态非"正在进行"，不可调度`
      }
    >
      {lineId}
    </div>
  );
};

interface ProductionLinesCellProps {
  task: Task;
  productionLineCount: number;
  densityStyles: DensityStyleValues;
  onDropVehicleOnLine: (vehicle: Vehicle, taskId: string, lineId: string) => void;
}
const ProductionLineBox = memo(ProductionLineBoxComponent);

const ProductionLinesCellComponent: React.FC<ProductionLinesCellProps> = ({
  task,
  productionLineCount,
  densityStyles,
  onDropVehicleOnLine,
}) => {
  if (task.dispatchStatus !== 'InProgress' || productionLineCount === 0) {
    return (
      <div
        className='text-center text-xs py-1'
        onContextMenu={e => {
          e.preventDefault();
          e.stopPropagation();
        }}
      >
        -
      </div>
    );
  }

  return (
    <div
      className='flex flex-row items-center justify-around h-full w-full px-0.5 py-0.5'
      style={{ gap: `${densityStyles.productionLineBoxGap || 2}px` }}
      onContextMenu={e => {
        e.preventDefault();
        e.stopPropagation();
      }}
    >
      {Array.from({ length: productionLineCount }, (_, i) => i + 1).map(line => {
        const lineId = `L${line}`;
        return (
          <ProductionLineBox
            key={lineId}
            task={task}
            lineId={lineId}
            densityStyles={densityStyles}
            onDropVehicle={onDropVehicleOnLine}
          />
        );
      })}
    </div>
  );
};
export const ProductionLinesCell = memo(ProductionLinesCellComponent);
