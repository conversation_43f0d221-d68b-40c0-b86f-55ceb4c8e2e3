/**
 * 重构后的车辆调度容器组件
 * 采用分层架构：UI组件只负责展示，业务逻辑通过Hook封装
 * 支持可移动、尺寸缩小、mock数据和API切换功能
 */

'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useTaskListSettings } from '@/features/task-management/hooks/useTaskListSettings';
import { useVehicleDispatchLogic } from '@/features/vehicle-dispatch/hooks/useVehicleDispatchLogic';
import { useVehicleContextMenu } from '@/features/vehicle-dispatch/hooks/useVehicleContextMenu';
import { useToast } from '@/shared/hooks/use-toast';
import { Card, CardContent } from '@/shared/components/card';
import { Button } from '@/shared/components/button';
import { Badge } from '@/shared/components/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSeparator,
} from '@/shared/components/dropdown-menu';
import { VehicleList } from './VehicleList';
import { DataSourceSwitcher } from '@/core/utils/dataSourceSwitcher';
import type { Vehicle } from '@/core/types';
import { Database, Cloud, Settings } from 'lucide-react';
import { DispatchedVehiclesContainer } from './DispatchedVehiclesContainer';
import { generateDispatchedVehicles } from '@/infrastructure/api/mock/mock-data';

/**
 * 固定版本的车辆调度容器组件
 * 用于右侧面板，固定在任务列表右侧
 */
export function FixedVehicleDispatchContainer({
  gridColumns = 4,
  onOpenSettings,
}: {
  gridColumns?: 2 | 3 | 4 | 5 | 6;
  onOpenSettings?: () => void;
}) {
  // 业务逻辑Hook
  const dispatchLogic = useVehicleDispatchLogic();
  const contextMenu = useVehicleContextMenu();
  const { toast } = useToast();

  // 设置Hook
  const { settings } = useTaskListSettings();
  const inTaskVehicleCardStyles = settings.inTaskVehicleCardStyles;
  const density = settings.density;

  // 数据源状态
  const [dataSource, setDataSource] = useState<'mock' | 'api'>(() =>
    DataSourceSwitcher.getCurrentMode()
  );

  // 已出厂车辆数据
  const [dispatchedVehiclesData] = useState(() => generateDispatchedVehicles(20));

  // 数据源切换
  const handleDataSourceToggle = () => {
    const newMode = DataSourceSwitcher.toggleMode();
    setDataSource(newMode);
    // 刷新页面以应用新的数据源
    window.location.reload();
  };

  // 已出厂车辆右键菜单处理
  const handleDispatchedVehicleContextMenu = useCallback(
    (event: React.MouseEvent, vehicle: any) => {
      event.preventDefault();
      event.stopPropagation();

      // 使用现有的车辆右键菜单系统
      contextMenu.openContextMenu(event, vehicle);
    },
    [contextMenu]
  );

  // 已出厂车辆点击处理
  const handleDispatchedVehicleClick = useCallback(
    (vehicle: any) => {
      toast({
        title: '车辆详情',
        description: `车辆号: ${vehicle.vehicleNumber || vehicle.id}`,
      });
    },
    [toast]
  );

  return (
    <div className='h-full flex flex-col bg-white border border-gray-200 rounded-lg'>
      {/* 标题栏 */}
      <div
        className='flex items-center justify-between p-1 flex-shrink-0'
        style={{ backgroundColor: 'hsl(var(--block-title))' }}
      >
        <div className='flex items-center gap-2'>
          <span className='text-sm font-medium'>车辆调度</span>
          <Badge variant={dataSource === 'mock' ? 'secondary' : 'default'} className='text-xs'>
            {dataSource === 'mock' ? 'Mock' : 'API'}
          </Badge>
        </div>

        <div className='flex items-center gap-1'>
          {/* 设置按钮 */}
          {onOpenSettings && (
            <Button
              variant='ghost'
              size='sm'
              onClick={onOpenSettings}
              className='h-6 w-6 p-0'
              title='设置'
            >
              <Settings className='w-3 h-3' />
            </Button>
          )}

          {/* 数据源切换按钮 */}
          <Button
            variant='ghost'
            size='sm'
            onClick={handleDataSourceToggle}
            className='h-6 w-6 p-0'
            title={`切换到${dataSource === 'mock' ? 'API' : 'Mock'}数据`}
          >
            {dataSource === 'mock' ? (
              <Database className='w-3 h-3' />
            ) : (
              <Cloud className='w-3 h-3' />
            )}
          </Button>
        </div>
      </div>

      {/* 内容区域 */}
      <div className='flex-1 overflow-hidden'>
        <div className='h-full flex flex-col gap-2'>
          {/* 待发车辆 - 占1/4高度 */}
          <Card className='flex-1 flex flex-col min-h-0'>
            <div className='p-1 text-xs'>待发车辆 ({dispatchLogic.pendingVehicles.length})</div>
            <CardContent className='pt-0 p-1 flex-1 overflow-hidden'>
              <div className='h-full overflow-y-auto vehicle-dispatch-table-scrollbar'>
                <VehicleList
                  title=''
                  vehicles={dispatchLogic.pendingVehicles}
                  listType='pending'
                  globalDispatchActive={dispatchLogic.globalDispatchActive}
                  displayMode={dispatchLogic.vehicleDisplayMode}
                  inTaskVehicleCardStyles={inTaskVehicleCardStyles}
                  density={density}
                  columns={gridColumns}
                  onContextMenu={contextMenu.openContextMenu}
                  onVisualMove={dispatchLogic.handleVisualMove}
                  onCommitReorder={dispatchLogic.handleCommitReorder}
                />
              </div>
            </CardContent>
          </Card>

          {/* 退货车辆 - 占1/4高度 */}
          <Card className='flex-1 flex flex-col min-h-0'>
            <div className='p-1 text-xs'>退货车辆 ({dispatchLogic.returnedVehicles.length})</div>
            <CardContent className='pt-0 p-1 flex-1 overflow-hidden'>
              <div className='h-full overflow-y-auto vehicle-dispatch-table-scrollbar'>
                <VehicleList
                  title=''
                  vehicles={dispatchLogic.returnedVehicles}
                  listType='returned'
                  globalDispatchActive={dispatchLogic.globalDispatchActive}
                  displayMode={dispatchLogic.vehicleDisplayMode}
                  inTaskVehicleCardStyles={inTaskVehicleCardStyles}
                  density={density}
                  columns={gridColumns}
                  onContextMenu={contextMenu.openContextMenu}
                  onVisualMove={dispatchLogic.handleVisualMove}
                  onCommitReorder={dispatchLogic.handleCommitReorder}
                />
              </div>
            </CardContent>
          </Card>

          {/* 已出厂车辆 - 占1/3高度，使用新表格显示 */}
          <Card className='flex-1 flex flex-col min-h-0'>
            <div className='p-1 text-xs'>已出厂车辆 ({dispatchedVehiclesData.length})</div>
            <CardContent className='pt-0 flex-1 overflow-hidden p-1'>
              <DispatchedVehiclesContainer
                data={dispatchedVehiclesData}
                className='h-full'
                onRowContextMenu={handleDispatchedVehicleContextMenu}
                onRowClick={handleDispatchedVehicleClick}
              />
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 右键菜单 */}
      {contextMenu.contextMenuOpen &&
        contextMenu.contextMenuPosition &&
        contextMenu.contextMenuVehicle && (
          <DropdownMenu
            open={contextMenu.contextMenuOpen}
            onOpenChange={contextMenu.closeContextMenu}
          >
            <DropdownMenuPortal>
              <DropdownMenuContent
                style={{
                  position: 'fixed',
                  left: contextMenu.contextMenuPosition?.x || 0,
                  top: contextMenu.contextMenuPosition?.y || 0,
                }}
                onCloseAutoFocus={e => e.preventDefault()}
              >
                {contextMenu.contextMenuVehicle &&
                  contextMenu
                    .getAvailableActions(contextMenu.contextMenuVehicle)
                    .map((action, index) => (
                      <React.Fragment key={action.id}>
                        <DropdownMenuItem
                          onClick={() =>
                            contextMenu.handleVehicleAction(
                              action.id,
                              contextMenu.contextMenuVehicle!
                            )
                          }
                        >
                          {action.label}
                        </DropdownMenuItem>
                        {index <
                          contextMenu.getAvailableActions(contextMenu.contextMenuVehicle!).length -
                            1 && <DropdownMenuSeparator />}
                      </React.Fragment>
                    ))}
              </DropdownMenuContent>
            </DropdownMenuPortal>
          </DropdownMenu>
        )}
    </div>
  );
}

/**
 * 原始的车辆调度容器组件（保持向后兼容）
 * 默认使用固定版本，适合右侧面板
 */
export function VehicleDispatchContainer() {
  return <FixedVehicleDispatchContainer gridColumns={4} />;
}
