// src/features/vehicle-dispatch/components/DispatchedVehiclesContainer.tsx
'use client';

import React, { useMemo } from 'react';

import { cn } from '@/core/lib/utils';
import type { DispatchedVehicle, DensityStyleValues } from '@/core/types';

import { useDispatchedVehiclesSettings } from '../hooks/use-dispatched-vehicles-settings';
import { useDispatchedVehiclesColumns } from '../hooks/use-dispatched-vehicles-columns';
import { DispatchedVehiclesTable } from './DispatchedVehiclesTable';

interface DispatchedVehiclesContainerProps {
  data: DispatchedVehicle[];
  className?: string;
  // 右键菜单功能
  onRowContextMenu?: (event: React.MouseEvent, vehicle: DispatchedVehicle) => void;
  onRowClick?: (vehicle: DispatchedVehicle) => void;
}

/**
 * 已出厂车辆容器组件
 */
export const DispatchedVehiclesContainer: React.FC<DispatchedVehiclesContainerProps> = ({
  data,
  className,
  onRowContextMenu,
  onRowClick,
}) => {
  // 使用设置Hook
  const { settings, onColumnOrderChange, onColumnVisibilityChange, onColumnSizingChange } =
    useDispatchedVehiclesSettings();

  // 密度样式配置
  const densityStyles: DensityStyleValues & { density: 'compact' | 'normal' | 'comfortable' } =
    useMemo(() => {
      switch (settings.density) {
        case 'compact':
          return {
            density: 'compact',
            headerPaddingX: 'px-2',
            headerPaddingY: 'py-1',
            headerHeight: 'h-8',
            headerFontSize: 'text-xs',
            cellPaddingX: 'px-2',
            cellPaddingY: 'py-1',
            cellFontSize: 'text-xs',
            cellFontWeight: 'font-normal',
            productionLineBoxSize: 'w-6 h-6',
            productionLineBoxFontSize: 'text-xs',
            productionLineBoxNumericWidth: 24,
            productionLineBoxGap: 4,
            cellHorizontalPaddingNumeric: 8,
          };
        case 'comfortable':
          return {
            density: 'comfortable',
            headerPaddingX: 'px-4',
            headerPaddingY: 'py-3',
            headerHeight: 'h-12',
            headerFontSize: 'text-base',
            cellPaddingX: 'px-4',
            cellPaddingY: 'py-3',
            cellFontSize: 'text-base',
            cellFontWeight: 'font-normal',
            productionLineBoxSize: 'w-10 h-10',
            productionLineBoxFontSize: 'text-sm',
            productionLineBoxNumericWidth: 40,
            productionLineBoxGap: 8,
            cellHorizontalPaddingNumeric: 16,
          };
        default:
          return {
            density: 'normal',
            headerPaddingX: 'px-3',
            headerPaddingY: 'py-2',
            headerHeight: 'h-10',
            headerFontSize: 'text-sm',
            cellPaddingX: 'px-3',
            cellPaddingY: 'py-2',
            cellFontSize: 'text-sm',
            cellFontWeight: 'font-normal',
            productionLineBoxSize: 'w-8 h-8',
            productionLineBoxFontSize: 'text-xs',
            productionLineBoxNumericWidth: 32,
            productionLineBoxGap: 6,
            cellHorizontalPaddingNumeric: 12,
          };
      }
    }, [settings.density]);

  // 使用列配置Hook
  const { tableColumns, tableTotalWidth } = useDispatchedVehiclesColumns({
    columnOrder: settings.columnOrder,
    columnVisibility: settings.columnVisibility,
    columnWidths: settings.columnWidths,
    densityStyles,
  });

  return (
    <div className={cn('flex flex-col h-full bg-white-100', className)}>
      {/* 表格内容 */}
      <DispatchedVehiclesTable
        data={data}
        columns={tableColumns}
        columnOrder={settings.columnOrder}
        columnVisibility={settings.columnVisibility}
        columnWidths={settings.columnWidths}
        enableZebraStriping={settings.enableZebraStriping}
        densityStyles={densityStyles}
        totalTableWidth={tableTotalWidth}
        onColumnOrderChange={onColumnOrderChange}
        onColumnVisibilityChange={onColumnVisibilityChange}
        onColumnSizingChange={onColumnSizingChange}
        onRowContextMenu={onRowContextMenu}
        onRowClick={onRowClick}
      />
    </div>
  );
};
